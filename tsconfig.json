{"compileOnSave": false, "compilerOptions": {"allowSyntheticDefaultImports": true, "baseUrl": "./", "downlevelIteration": true, "outDir": "./dist/out-tsc", "paths": {"@avaliacao-fisica-core/*": ["src/app/avaliacao-fisica/avaliacao-fisica-core/*"], "@crossfit-core/*": ["src/app/crossfit/crossfit-core/*"], "@treino-core/*": ["src/app/treino/treino-core/*"], "@base-core/*": ["src/app/base/base-core/*"], "@base-shared/*": ["src/app/base/base-shared/*"], "@pacto-commom/*": ["src/app/pacto-common/*"], "old-ui-kit": ["dist/old-ui-kit"], "old-ui-kit/*": ["dist/old-ui-kit/*"], "ui-kit": ["dist/ui-kit"], "ui-kit/*": ["dist/ui-kit/*"], "canal-cliente": ["projects/canal-cliente/src"], "canal-cliente/*": ["projects/canal-cliente/src/*"], "sdk": ["dist/sdk"], "sdk/*": ["dist/sdk/*"], "treino-api": ["dist/treino-api"], "treino-api/*": ["dist/treino-api/*"], "zw-pactopay-api": ["dist/zw-pactopay-api"], "zw-pactopay-api/*": ["dist/zw-pactopay-api/*"], "zw-servlet-api": ["dist/zw-servlet-api"], "zw-servlet-api/*": ["dist/zw-servlet-api/*"], "cadastro-aux-api": ["dist/cadastro-aux-api"], "cadastro-aux-api/*": ["dist/cadastro-aux-api/*"], "plano-api": ["dist/plano-api"], "plano-api/*": ["dist/plano-api/*"], "acesso-sistema-api": ["dist/acesso-sistema-api"], "acesso-sistema-api/*": ["dist/acesso-sistema-api/*"], "produto-api": ["dist/produto-api"], "produto-api/*": ["dist/produto-api/*"], "adm-core-api": ["dist/adm-core-api"], "adm-core-api/*": ["dist/adm-core-api/*"], "pactopay-api": ["dist/pactopay-api"], "pactopay-api/*": ["dist/pactopay-api/*"], "clube-vantagens-api": ["dist/clube-vantagens-api"], "clube-vantagens-api/*": ["dist/clube-vantagens-api/*"], "relatorio-api": ["dist/relatorio-api"], "relatorio-api/*": ["dist/relatorio-api/*"], "ms-pactopay-api": ["dist/ms-pactopay-api"], "ms-pactopay-api/*": ["dist/ms-pactopay-api/*"], "crm-api": ["dist/crm-api"], "crm-api/*": ["dist/crm-api/*"], "pacto-api": ["dist/pacto-api"], "pacto-api/*": ["dist/pacto-api/*"], "login-app-api": ["dist/login-app-api"], "login-app-api/*": ["dist/login-app-api/*"], "midia-social-api": ["dist/midia-social-api"], "midia-social-api/*": ["dist/midia-social-api/*"], "adm-ms-api": ["dist/adm-ms-api"], "adm-ms-api/*": ["dist/adm-ms-api/*"], "integracao-gympass-api": ["dist/integracao-gympass-api"], "integracao-gympas-api/*": ["dist/integracao-gympass-api/*"], "pacto-layout": ["dist/pacto-layout"], "pacto-layout/*": ["dist/pacto-layout/*"], "adm-legado-api": ["dist/adm-legado-api"], "adm-legado-api/*": ["dist/adm-legado-api/*"], "marketing-api": ["dist/marketing-api"], "marketing-api/*": ["dist/marketing-api/*"], "notificacao-api": ["dist/notificacao-api"], "notificacao-api/*": ["dist/notificacao-api/*"], "financeiro-ms-api": ["dist/financeiro-ms-api"], "financeiro-ms-api/*": ["dist/financeiro-ms-api/*"], "pessoa-ms-api": ["dist/pessoa-ms-api"], "pessoa-ms-api/*": ["dist/pessoa-ms-api/*"], "recurso-ms-api": ["dist/recurso-ms-api"], "recurso-ms-api/*": ["dist/recurso-ms-api/*"], "autenticacao-api": ["dist/autenticacao-api"], "autenticacao-api/*": ["dist/autenticacao-api/*"], "ngx-image-compress": ["node_modules/ngx-image-compress-legacy"], "bi-ms-api": ["dist/bi-ms-api"], "bi-ms-api/*": ["dist/bi-ms-api/*"], "@module/adm/*": ["dist/adm/*"], "@adm/*": ["projects/adm/src/app/*"]}, "sourceMap": true, "declaration": false, "module": "esnext", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2015", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"]}}