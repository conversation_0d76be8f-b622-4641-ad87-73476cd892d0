stages:
    - build
    - deploy
    - test
    - finish

variables:
    IMAGE: 'registry.gitlab.com/plataformazw/zw_ui:$CI_COMMIT_REF_SLUG'

build-libs:
    image: node:12
    tags:
        - docker
        - large
        - oci
    variables:
        ENABLE_LOGIN_CAPTCHA: 'false'
        LOAD_SCRIPTS_HTML: 'true'
    interruptible: true
    only:
        - merge_requests
        - develop
        - release/RC
        - master
    except:
        variables:
            - $RELEASE_TAG
            - $CI_COMMIT_MESSAGE =~ /Bump version:/
    artifacts:
        paths:
            - dist/
            - src/environments/environment.ts
            - projects/sdk/src/lib/environments/environment.ts
            - projects/crm/src/environments/environment.ts
            - projects/login/src/environments/environment.ts
            - projects/adm/src/environments/environment.ts
            - projects/marketing/src/environments/environment.ts
    script:
        - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
        - npm rebuild node-sass
        - yarn run config -du {DISCOVERY_URL} -enl \'{ENABLE_NEW_LOGIN}\' -elc \'{ENABLE_LOGIN_CAPTCHA}\' -lsh \'{LOAD_SCRIPTS_HTML}\'
        - NODE_OPTIONS=--max-old-space-size=32768 yarn build-libs 2> >(grep -v warning 1>&2)

build-treino:
    image: node:12
    tags:
        - docker
        - large
        - oci
    variables:
        ENABLE_LOGIN_CAPTCHA: 'false'
    interruptible: true
    only:
        - merge_requests
        - develop
        - release/RC
        - master
    except:
        variables:
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
            - $CI_COMMIT_MESSAGE =~ /Bump version:/
    needs:
        - build-libs
    dependencies:
        - build-libs
    artifacts:
        paths:
            - dist/treino
    script:
        - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
        - npm rebuild node-sass
        - NODE_OPTIONS=--max-old-space-size=32768 yarn build-treino 2> >(grep -v warning 1>&2)

.docker-build: &docker-build-template
    variables:
        DOCKER_TLS_CERTDIR: ''
        CONTEXT: ''
    tags:
        - large
        - shell
        - oci
    only:
        - merge_requests
        - develop
        - release/RC
        - master
    script:
        - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
        - docker build -t $IMAGE --build-arg app_dir=$APP_DIR --build-arg context=$CONTEXT .
        - docker push $IMAGE

docker-build-treino:
    extends: .docker-build
    interruptible: true
    dependencies:
        - build-treino
    needs:
        - build-treino
    artifacts:
        paths:
            - dist/treino
    except:
        variables:
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
            - $CI_COMMIT_MESSAGE =~ /Bump version:/
    variables:
        IMAGE: 'registry.gitlab.com/plataformazw/zw_ui/novo-treino:$CI_COMMIT_REF_SLUG'
        APP_DIR: 'treino'

tag-version:
    image: keviocastro/node-build
    interruptible: true
    when: on_success
    tags:
        - docker
        - large
        - oci
    needs:
        - build-treino
    except:
        variables:
            - $CI_COMMIT_MESSAGE =~ /Bump version:/
            - $DEPLOY_LOGIN_PROD
            - $DEPLOY_UI_PROD
            - $RELEASE_TAG
    only:
        - master
    script:
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        - git config --global user.name "Pipeline"
        - git config --global user.email "<EMAIL>"
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
        - npm version patch -m 'Bump version:%s'
        - git tag -f latest -m "Deploy tag"
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH -f --tags

tag-version-2:
    image: keviocastro/node-build
    interruptible: true
    when: on_success
    tags:
        - docker
        - large
        - oci
    only:
        variables:
            - $RELEASE_TAG
    script:
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
        - git config --global user.name "Pipeline"
        - git config --global user.email "<EMAIL>"
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
        - npm version patch -m 'Bump version:%s'
        - git tag -f latest -m "Deploy tag"
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
        - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH -f --tags

deploy-login-prod-on-master:
    image: registry.gitlab.com/plataformazw/docker-pacto/deploy-node-aws:master
    interruptible: true
    only:
        variables:
            - $DEPLOY_LOGIN_PROD
    variables:
        DISCOVERY_URL: 'https://discovery.ms.pactosolucoes.com.br'
        ENABLE_LOGIN_CAPTCHA: 'true'
        ENABLE_NEW_LOGIN: 'true'
    tags:
        - docker
        - large
        - oci
    stage: deploy
    artifacts:
        paths:
            - dist/
    script:
        - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
        - npm rebuild node-sass
        - node ./setup-env.js -du ${DISCOVERY_URL} -enl ${ENABLE_NEW_LOGIN} -elc ${ENABLE_LOGIN_CAPTCHA}
        - NODE_OPTIONS=--max-old-space-size=32768 yarn build-libs-login 2> >(grep -v warning 1>&2)
        - NODE_OPTIONS=--max-old-space-size=32768 yarn build-login-all 2> >(grep -v warning 1>&2)
        - aws s3 sync ./dist/login s3://$BUCKET_LOGIN_PROD/ --delete
        - aws cloudfront create-invalidation --distribution-id E1BXNJ1V5YN3W0 --paths "/*"

deploy-zw-ui-prod:
    image: registry.gitlab.com/plataformazw/docker-pacto/deploy-node-aws:master
    interruptible: true
    only:
        variables:
            - $DEPLOY_UI_PROD
    tags:
        - docker
        - large
        - oci
    stage: deploy
    script:
        - aws ecs update-service --cluster pacto-ui-prod --service pacto-ui-prod-service --load-balancers targetGroupArn=arn:aws:elasticloadbalancing:sa-east-1:335687144049:targetgroup/tg-pacto-zw-ui-prod/85cbaa4eaafe0e28,containerName=zw-ui-prod,containerPort=80 --force-new-deployment --region sa-east-1 --task-definition zw-ui-prod


include:
    - local: .gitlab-ci-stages.yml
