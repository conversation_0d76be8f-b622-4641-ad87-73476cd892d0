# Plataforma Pacto

## Introdução

Monorepo front-end desenvolvido em Angular 8 gerado com [Angular Cli](https://angular.io/cli). 

Os produtos implementados nesse repositório e seus respectivos times responsáveis são: 

| Produto          |  Time Responsável  |
|    :---          |  :---              |
| PactoPay         |  Payments          |
| Adm              |  AD                |
| Treino           |  Kaizen            | 
| Graduação        |  Kaizen            |
| Pessoas          |  Kaizen            |  
| Crossfit         |  Kaizen            | 
| Avaliação        |  Kaizen            | 
| Agenda           |  Kaizen            |  
| Canal            |  ?                 |

## Arquitetura do Sistema
Para entender como a plataforma é organizada veja esse [documento](/documentation/arquitetura.md).

## Gerenciando o repositório com NX
[NX](https://nx.dev/) é uma alternativa do Angular CLI para gerenciar o monorepo. Para ler mais sobre isso veja esse [documento](/documentation/gerenciando-monorepo-com-nx.md). Em breve o monorepo será migrado.

## Comunicação com API's
Para mais informaçõe sobre padrões e detalhes de como se comunidar com as diferentes API's da plataforma veja esse [documento.](/documentation/adaptadores-api.md)

## Construindo Relatórios escaláveis
Para mais informações sobre padrões e detalhes de como construir tabelas e data tables veja esse [documento.](/documentation/data-tables.md)

## Construindo Formulários escaláveis
Para mais informações sobre padrões e princípios de como construir formulários veja esse [documento.](/documentation/formularios.md)

## Criação e manutenção do catálogo de componentes
Para mais informações sobre padrões, princípios e boas práticas para criação e manutenção do catalogo de componentes veja esse [documento.](/documentation/boas-praticas-ui-kit.md)

## Organização de controladores e rotas
Para mais informações sobre padrões, princípios e boas práticas relacionadas a organização de rotas e contraladores veja esse [documento.](/documentation/controladores-e-rotas.md)

## Code Quality Checklist
Esse [documento](./documentation/code-quality-checklist.md) possui uma lista resumida dos principais padrões e conceitos que devem ser seguidos no desenvolvimento do projeto. Esse material deve ser usado como critério ao se fazer code review.


### Como configurar o ambiente de desenvolvimento ?

1) Instale as dependências

```
npm install
```

2) Configure o ambiente de desenvolvimento

```
npm run config
```

3) Compile os projetos

```
npm run build
```

4) Inicie o projeto que você vai desenvolver

> Treino
```
npm start
```
 

> Adm
```
npm run start-adm
```

> Login
```
npm run start-login
```

* Consulte o arquivo package.json para ver os comando de start de todos os projetos

### Para realizar deploy em produção

> Requirements: Nodejs 12.x

> Instale as dependências
```
npm install
```

> Configure o as variabeis de ambiente
```
npm run config -- -du https://discovery.ms.pactosolucoes.com.br -enl true -elc true
```

> Compile todas as aplicações
```
npm run build-all
```

>
Todas as aplicações serão compiladas para todas as linguagens e estarão disponíveis na past `dist` para serem deployadas.


### Antes de commit

Execute o comando abaixo para verificar se está dentro dos padrões. Caso não faça essa verificação seu commit pode ser rejeitado.
```
> ng lint plataforma-pacto
```
Para identificar os erros de forma mais prática configure o plugin TSLint na sua ide. [Visual Studio Code](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-tslint-plugin).

### Como buildar o sistema (somente Linux) 
Basta fornecer 3 parâmetros:
1. -y: endereço do discovery
2. -u: url relativo de onde o sistema estará disponível, por exemplo: se o sistema está disponível na url http://plataforma.pactosolucoes.com/nova-plataforma o parâmetro u deve ser **/nova-plataforma/**
3. -l: **pt** para portugues ou **es** para espanhol
```
> ./scripts/build-spa.sh -y 'discovery url' -u '/' -l 'pt';
```
## Canal do Cliente
:exclamation: *Para informações sobre como configurar o Canal do Cliente acesse [documento.](/projects/canal-cliente)*

#
