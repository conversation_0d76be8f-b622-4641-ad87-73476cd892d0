FROM nginx:alpine

WORKDIR /usr/share/nginx/html

ENV PROJECT_NAME='ZW UI'
ENV DISCOVERY_URL='{DISCOVERY_URL}'
ENV ENABLE_NEW_LOGIN='{ENABLE_NEW_LOGIN}'
ENV ENABLE_LOGIN_CAPTCHA='{ENABLE_LOGIN_CAPTCHA}'
ENV LOAD_SCRIPTS_HTML="${LOAD_SCRIPTS_HTML:-false}"
ENV SERVER_PATH="${SERVER_PATH:-''}"

RUN apk add --update nodejs npm
COPY ./docker/bin /docker/bin
RUN cd /docker/bin && \
    npm install && \
    npm link

# Não precisa mais copiar os arquivos aqui
ARG app_dir=${app_dir:-"treino"}
ARG context=${context:-"docker"}
ENV INITIAL_CONTEXT=$context

COPY docker/conf/nginx.conf /etc/nginx/nginx.conf
COPY docker/conf/default.conf /etc/nginx/conf.d/default.conf
COPY docker/conf/default.conf.template /etc/nginx/conf.d/default.conf.template

COPY ./dist/$app_dir/ /usr/share/nginx/html/$context

COPY docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

EXPOSE 80
ENTRYPOINT [ "sh", "/entrypoint.sh" ]
