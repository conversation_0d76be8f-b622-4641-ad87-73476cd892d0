version: "3.9"

services:
  treino:
    image: registry.gitlab.com/plataformazw/zw_ui/novo-treino:feature-e4-263
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - app_dir=treino
    environment:
      PROJECT_NAME: "Sistema Pacto"
      DISCOVERY_URL: http://host.docker.internal:8101
      ENABLE_NEW_LOGIN: false
      ENABLE_LOGIN_CAPTCHA: false
    ports:
      - 8000:80
  adm:
    image: registry.gitlab.com/plataformazw/zw_ui/adm:feature-e4-263
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - app_dir=adm
    environment:
      PROJECT_NAME: "Sistema Pacto"
      DISCOVERY_URL: http://host.docker.internal:8101
      ENABLE_NEW_LOGIN: false
      ENABLE_LOGIN_CAPTCHA: false
    ports:
      - 8003:80
  crm:
    image: registry.gitlab.com/plataformazw/zw_ui/crm:master
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - app_dir=crm
    environment:
      PROJECT_NAME: "Sistema Pacto"
      DISCOVERY_URL: http://host.docker.internal:8101
      ENABLE_NEW_LOGIN: false
      ENABLE_LOGIN_CAPTCHA: false
    ports:
      - 8004:80
  marketing:
    image: registry.gitlab.com/plataformazw/zw_ui/crm:master
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - app_dir=marketing
    environment:
      PROJECT_NAME: "Sistema Pacto"
      DISCOVERY_URL: http://host.docker.internal:8101
      ENABLE_NEW_LOGIN: false
      ENABLE_LOGIN_CAPTCHA: false
    ports:
      - 8005:80
  nichos:
    image: registry.gitlab.com/plataformazw/zw_ui/nichos:feature-e04-66
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - app_dir=nichos
    environment:
      PROJECT_NAME: "Sistema Pacto"
      DISCOVERY_URL: http://host.docker.internal:8101
      ENABLE_NEW_LOGIN: false
      ENABLE_LOGIN_CAPTCHA: false
    ports:
      - 8006:80