import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AcessoSistemaApiModule } from "../../acesso-sistema-api.module";
import { AcessoSistemaApiBaseService } from "../../base/acesso-sistema-api-base.service";
import { ApiResponseList, DataFiltro } from "../../base/base.model";
import {
	IntegracaoAcessoEmpresa,
	IntegracaoAcessoGrupoEmpresarial,
	LocalAcesso,
} from "./integracao-acesso.model";

@Injectable({
	providedIn: AcessoSistemaApiModule,
})
export class AcessoSistemaApiIntegracaoAcessoService {
	constructor(private admApiBaseService: AcessoSistemaApiBaseService) {}

	public list(
		filtros?: DataFiltro
	): Observable<ApiResponseList<Array<IntegracaoAcessoGrupoEmpresarial>>> {
		const page = filtros.page ? filtros.page : 0;
		const size = filtros.size ? filtros.size : 10;
		delete filtros.page;
		delete filtros.size;
		const params: any = {
			page,
			size,
			filters: JSON.stringify(filtros),
		};

		return this.admApiBaseService.get<
			ApiResponseList<Array<IntegracaoAcessoGrupoEmpresarial>>
		>(`integracaoAcesso`, {
			params,
		});
	}

	public save(
		integracaoAcessoGrupoEmpresa: IntegracaoAcessoGrupoEmpresarial
	): Observable<ApiResponseList<IntegracaoAcessoGrupoEmpresarial>> {
		return this.admApiBaseService.post<
			ApiResponseList<IntegracaoAcessoGrupoEmpresarial>
		>(`integracaoAcesso`, integracaoAcessoGrupoEmpresa);
	}

	public find(
		id: number | string
	): Observable<{ content: IntegracaoAcessoGrupoEmpresarial }> {
		return this.admApiBaseService.get(`integracaoAcesso/${id}`);
	}

	public delete(id: number): Observable<any> {
		return this.admApiBaseService.delete(`integracaoAcesso/${id}`);
	}

	public findAllEmpresasByKey(
		key
	): Observable<ApiResponseList<IntegracaoAcessoEmpresa>> {
		const params = {
			filters: JSON.stringify({ key }),
		};
		return this.admApiBaseService.get(`empresas/find-by-key`, { params });
	}

	public findAllLocaisAcesso(
		filtros
	): Observable<ApiResponseList<LocalAcesso>> {
		const params = {
			filters: JSON.stringify(filtros),
		};
		return this.admApiBaseService.get(`locaisAcesso/find-by-key-code`, {
			params,
		});
	}
}
