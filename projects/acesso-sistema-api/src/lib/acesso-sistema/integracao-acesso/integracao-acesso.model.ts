export class IntegracaoAcessoGrupoEmpresarial {
	codigo: number;
	descricao: string;
	nomeEmpresa: string;
	urlZillyonWeb: string;
	chave: string;
	empresaRemota: any;
	empresaLocal: any;
	localAcesso: any;
	coletor: any;
	terminal: number;
	codigoChaveIntegracaoDigitais: number;
}

export class IntegracaoAcessoEmpresa {
	codigo: number;
	nome: string;
	ativa: boolean;
}

export class LocalAcesso {
	codigo: number;
	descricao: string;
	empresa: any;
	coletores: Array<any>;
}

export class Coletor {
	codigo: number;
	descricao: string;
	numeroTerminal: number;
}
