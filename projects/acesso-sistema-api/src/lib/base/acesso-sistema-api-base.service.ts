import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { Injectable, Optional } from "@angular/core";
import { Observable } from "rxjs";
import { mergeMap } from "rxjs/operators";
import { AcessoSistemaApiModule } from "../acesso-sistema-api.module";
import {
	AcessoSistemaApiConfig,
	AcessoSistemaApiConfigProviderBase,
} from "./acesso-sistema-api-config-provider-base.service";

interface RequestOptions {
	headers?: HttpHeaders | { [header: string]: string | string[] };
	params?: HttpParams | { [param: string]: string | string[] };

	[key: string]: any;
}

@Injectable({
	providedIn: AcessoSistemaApiModule,
})
export class AcessoSistemaApiBaseService {
	private constructor(
		private httpClient: HttpClient,
		@Optional() private apiConfigProvider: AcessoSistemaApiConfigProviderBase
	) {
		if (!this.apiConfigProvider) {
			throw Error(
				"Não foi fornecida uma implementação para AdmApiConfigProviderBase"
			);
		}
	}

	public get<T>(url: string, options: RequestOptions = {}): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.get<T | any>(fullUrl, mergedOptions);
			})
		);
	}

	public delete<T>(url: string, options: RequestOptions = {}): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.delete<T>(fullUrl, mergedOptions);
			})
		);
	}

	public put<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.put<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	public post<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.post<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	private mergeOptions(
		options: RequestOptions,
		apiConfig: AcessoSistemaApiConfig
	): RequestOptions {
		const headers = options.header ? options.header : {};
		const mergedHeaders = {
			...headers,
			Authorization: `Bearer ${apiConfig.authToken}`,
			empresaId: apiConfig.empresaId,
		};

		if (options) {
			options.headers = mergedHeaders;
			return options;
		} else {
			return { headers: mergedHeaders };
		}
	}

	private buildUrl(baseUrl: string, relativeUrl: string) {
		return `${baseUrl}/${relativeUrl}`;
	}
}
