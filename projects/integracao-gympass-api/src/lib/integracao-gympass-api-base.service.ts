import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { Injectable, Optional } from "@angular/core";
import { IntegracaoGympassApiModule } from "./integracao-gympass-api.module";
import {
	IntegracaoGympassApiConfig,
	IntegracaoGympassApiConfigProviderBase,
} from "./integracao-gympass-api-config-provider-base.service";
import { Observable } from "rxjs";
import { mergeMap } from "rxjs/operators";

interface RequestOptions {
	[key: string]: any;

	headers?: HttpHeaders | { [header: string]: string | string[] };
	params?: HttpParams | { [param: string]: string | string[] };
}

@Injectable({
	providedIn: IntegracaoGympassApiModule,
})
export class IntegracaoGympassApiBaseService {
	private constructor(
		private httpClient: HttpClient,
		@Optional()
		private apiConfigProvider: IntegracaoGympassApiConfigProviderBase
	) {
		if (!this.apiConfigProvider) {
			throw Error(
				"Não foi fornecida uma implementação para IntegracaoGympassApiConfigProviderBase"
			);
		}
	}

	private mergeOptions(
		options: RequestOptions,
		apiConfig: IntegracaoGympassApiConfig
	): RequestOptions {
		const headers = options.header ? options.header : {};
		const mergedHeaders = {
			...headers,
			Authorization: `Bearer ${apiConfig.authToken}`,
			empresaId: apiConfig.empresaId,
		};
		options.headers = mergedHeaders;

		const params = options.params ? options.params : {};
		options.params = params;
		return options;
	}

	public get(url: string, options: RequestOptions = {}): Observable<Object> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = `${apiConfig.baseUrl}/${url}`;
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.get(fullUrl, mergedOptions);
			})
		);
	}

	public delete(url: string, options: RequestOptions = {}): Observable<Object> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = `${apiConfig.baseUrl}/${url}`;
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.delete(fullUrl, mergedOptions);
			})
		);
	}

	public put(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<Object> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = `${apiConfig.baseUrl}/${url}`;
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.put(fullUrl, body, mergedOptions);
			})
		);
	}

	public post(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<Object> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = `${apiConfig.baseUrl}/${url}`;
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.post(fullUrl, body, mergedOptions);
			})
		);
	}

	public patch(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<Object> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = `${apiConfig.baseUrl}/${url}`;
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.patch(fullUrl, body, mergedOptions);
			})
		);
	}
}
