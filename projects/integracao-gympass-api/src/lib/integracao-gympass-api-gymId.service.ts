import { Injectable } from "@angular/core";
import { IntegracaoGympassApiModule } from "./integracao-gympass-api.module";
import { IntegracaoGympassApiBaseService } from "./integracao-gympass-api-base.service";
import { Observable } from "rxjs";
import { ApiResponseSingle } from "./base.model";
import { catchError, map } from "rxjs/operators";
import { GymDTO } from "./gympass.model";

@Injectable({
	providedIn: IntegracaoGympassApiModule,
})
export class IntegracaoGympassApiGymIdService {
	constructor(private restService: IntegracaoGympassApiBaseService) {}

	isGymIdConfigurado(gymId: string): Observable<ApiResponseSingle<boolean>> {
		return this.restService.get(`gymid/is-configurado/${gymId}`, {}).pipe(
			map((response: ApiResponseSingle<boolean>) => {
				return response.content;
			}),
			catchError((error) => {
				return new Observable((observer) => {
					observer.error(error);
					observer.complete();
				});
			})
		);
	}

	gravarGymId(gymDTO: GymDTO): Observable<ApiResponseSingle<any>> {
		return this.restService.post(`gymid`, gymDTO).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			}),
			catchError((error) => {
				return new Observable((observer) => {
					observer.error(error);
					observer.complete();
				});
			})
		);
	}

	gravarGymIdTodasEmpresas(
		gymDTOs: GymDTO[]
	): Observable<ApiResponseSingle<any>> {
		return this.restService.post(`gymid/insert-configs-gympass`, gymDTOs).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			}),
			catchError((error) => {
				return new Observable((observer) => {
					observer.error(error);
					observer.complete();
				});
			})
		);
	}
}
