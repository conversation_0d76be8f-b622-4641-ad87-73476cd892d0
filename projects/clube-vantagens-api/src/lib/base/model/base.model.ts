export interface ApiResponseSingle<T> {
	meta?: MetaError;
	content?: T;
}

export interface ApiResponseList<T> {
	meta?: MetaError;
	content?: Array<T>;
	totalElements?: number;
	totalPages?: number;
	last?: boolean;
	numberOfElements?: number;
	first?: boolean;
	size?: number;
	number?: number;
}

export interface MetaError {
	error: string;
	message: string;
}

export interface DataFiltro {
	quicksearchValue?: filterValue;
	quicksearchFields?: Array<filterValue>;
	filters?: { [key: string]: filterValue | Array<filterValue> };
	configs?: { [key: string]: any };
	page?: number;
	size?: number;
	/**
	 * ASC/DESC
	 */
	sortDirection?: string;
	sortField?: string;
}

export type filterValue = number | string;
