import { Injectable, Optional } from "@angular/core";
import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import {
	ClubeVantagensApiConfig,
	ClubeVantagensApiConfigProviderBase,
} from "./clube-vantagens-api-config-provider-base.service";
import { mergeMap } from "rxjs/operators";
import { Observable } from "rxjs";
import { ClubeVantagensApiModule } from "../clube-vantagens-api.module";

interface RequestOptions {
	[key: string]: any;

	headers?: HttpHeaders | { [header: string]: string | string[] };
	params?: HttpParams | { [param: string]: string | string[] };
}

@Injectable({
	providedIn: ClubeVantagensApiModule,
})
export class ClubeVantagensApiBaseService {
	private constructor(
		private httpClient: HttpClient,
		@Optional() private apiConfigProvider: ClubeVantagensApiConfigProviderBase
	) {
		if (!this.apiConfigProvider) {
			throw Error(
				"Não foi fornecida uma implementação para ClubeVantagensApiConfigProviderBase"
			);
		}
	}

	private mergeOptions(
		options: RequestOptions,
		apiConfig: ClubeVantagensApiConfig
	): RequestOptions {
		const headers = options.header ? options.header : {};
		const mergedHeaders = {
			...headers,
			Authorization: `Bearer ${apiConfig.authToken}`,
			empresaId: apiConfig.empresaId,
			"Accept-Language": apiConfig.acceptLanguage,
		};

		if (options) {
			options.headers = mergedHeaders;
			return options;
		} else {
			return { headers: mergedHeaders };
		}
	}

	public get<T>(url: string, options: RequestOptions = {}): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.get<T | any>(fullUrl, mergedOptions);
			})
		);
	}

	public delete<T>(url: string, options: RequestOptions = {}): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.delete<T>(fullUrl, mergedOptions);
			})
		);
	}

	public put<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.put<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	public post<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.post<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	private buildUrl(baseUrl: string, relativeUrl: string) {
		return `${baseUrl}/${relativeUrl}`;
	}
}
