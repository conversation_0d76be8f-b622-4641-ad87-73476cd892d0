import { Injectable } from "@angular/core";
import { ClubeVantagensApiModule } from "../clube-vantagens-api.module";
import { ClubeVantagensApiBaseService } from "../base/clube-vantagens-api-base.service";
import { Observable } from "rxjs";
import { ClubeVantagensBrinde } from "./brinde.model";
import { ApiResponseList, DataFiltro } from "../base/model/base.model";

@Injectable({
	providedIn: ClubeVantagensApiModule,
})
export class ClubeVantagensApiBrindeService {
	constructor(
		private clubeVantagensApiBaseService: ClubeVantagensApiBaseService
	) {}

	public list(
		filtros?: DataFiltro
	): Observable<ApiResponseList<Array<ClubeVantagensBrinde>>> {
		const page = filtros.page ? filtros.page : 0;
		const size = filtros.size ? filtros.size : 10;
		delete filtros.page;
		delete filtros.size;
		const params: any = {
			page,
			size,
			filters: JSON.stringify(filtros),
		};

		return this.clubeVantagensApiBaseService.get<
			ApiResponseList<Array<ClubeVantagensBrinde>>
		>(`brinde`, {
			params,
		});
	}

	public save(
		brinde: ClubeVantagensBrinde
	): Observable<ApiResponseList<ClubeVantagensBrinde>> {
		return this.clubeVantagensApiBaseService.post<
			ApiResponseList<ClubeVantagensBrinde>
		>(`brinde`, brinde);
	}

	public find(
		id: number | string
	): Observable<{ content: ClubeVantagensBrinde }> {
		return this.clubeVantagensApiBaseService.get(`brinde/${id}`);
	}

	public delete(id: number): Observable<any> {
		return this.clubeVantagensApiBaseService.delete(`brinde/${id}`);
	}
}
