import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { ApiResponseSingle, ApiResponseSingleV2 } from "../response.model";
import { FinanceiroMsApiBaseService } from "../financeiro-ms-api-base.service";
import { map } from "rxjs/operators";
import { ConfiguracaoFinanceiro } from "./financeiro-api-configuracao.model";

@Injectable({
	providedIn: "root",
})
export class FinanceiroMsApiConfiguracaoService {
	constructor(private financeiroMsBaseService: FinanceiroMsApiBaseService) {}

	alterar(config: ConfiguracaoFinanceiro): Observable<ConfiguracaoFinanceiro> {
		return this.financeiroMsBaseService.put(`v1/configuracoes`, config).pipe(
			map((response: ApiResponseSingle<ConfiguracaoFinanceiro>) => {
				return Array.isArray(response.content)
					? response.content[0]
					: response.content;
			})
		);
	}

	consultar(): Observable<ConfiguracaoFinanceiro> {
		return this.financeiroMsBaseService
			.get<ApiResponseSingle<ConfiguracaoFinanceiro>>("v1/configuracoes")
			.pipe(
				map((response: ApiResponseSingle<ConfiguracaoFinanceiro>) => {
					if (Array.isArray(response.content)) {
						return response.content[0];
					} else {
						return response.content;
					}
				})
			);
	}
}
