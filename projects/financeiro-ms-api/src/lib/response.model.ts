export interface ApiResponseSingleV2<T> {
	message?: Array<Message>;
	result?: Array<T>;
}

export interface Message {
	code: number;
	message: string;
}

export interface ApiResponseSingle<T> {
	meta?: MetaError;
	content?: T;
}

export interface ApiResponseList<T> {
	meta?: MetaError;
	content?: Array<T>;
	totalElements?: number;
	totalPages?: number;
	last?: boolean;
	numberOfElements?: number;
	first?: boolean;
	size?: number;
	number?: number;
}

export interface MetaError {
	error: string;
	message: string;
}
