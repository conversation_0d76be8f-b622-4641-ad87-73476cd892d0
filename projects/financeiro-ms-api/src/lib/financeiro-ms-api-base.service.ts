import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { Injectable, Optional } from "@angular/core";

import { mergeMap } from "rxjs/operators";
import { Observable } from "rxjs";

import { FinanceiroMsApiModule } from "./financeiro-ms-api.module";
import {
	FinanceiroMsApiConfig,
	FinanceiroMsApiConfigProviderBase,
} from "./financeiro-ms-api-config-provider-base.service";

interface RequestOptions {
	[key: string]: any;

	headers?: HttpHeaders | { [header: string]: string | string[] };
	params?: HttpParams | { [param: string]: string | string[] };
}

@Injectable({
	providedIn: FinanceiroMsApiModule,
})
export class FinanceiroMsApiBaseService {
	constructor(
		private httpClient: HttpClient,
		@Optional() private apiConfigProvider: FinanceiroMsApiConfigProviderBase
	) {
		if (!this.apiConfigProvider) {
			throw Error(
				"Não foi fornecida uma implementação para FinanceiroMsApiConfigProviderBase"
			);
		}
	}

	private mergeOptions(
		options: RequestOptions,
		apiConfig: FinanceiroMsApiConfig
	): RequestOptions {
		const headers = options.header ? options.header : {};
		const mergedHeaders = {
			...headers,
			Authorization: `Bearer ${apiConfig.authToken}`,
			empresaId: apiConfig.empresaId,
		};
		options.headers = mergedHeaders;

		const params = options.params ? options.params : {};
		options.params = params;
		return options;
	}

	public get<T>(url: string, options: RequestOptions = {}): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.get<T | any>(fullUrl, mergedOptions);
			})
		);
	}

	public delete<T>(url: string, options: RequestOptions = {}): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.delete<T>(fullUrl, mergedOptions);
			})
		);
	}

	public put<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.put<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	public post<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.post<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	public patch<T>(
		url: string,
		body: any,
		options: RequestOptions = {}
	): Observable<T> {
		return this.apiConfigProvider.getApiConfig().pipe(
			mergeMap((apiConfig) => {
				const fullUrl = this.buildUrl(apiConfig.baseUrl, url);
				const mergedOptions = this.mergeOptions(options, apiConfig);
				return this.httpClient.patch<T>(fullUrl, body, mergedOptions);
			})
		);
	}

	private buildUrl(baseUrl: string, relativeUrl: string) {
		return `${baseUrl}/${relativeUrl}`;
	}
}
