import { Injectable } from "@angular/core";
import { PactoLayoutSDKWrapper } from "../../sdk-wrapper/sdk-wrappers";
import {
	ApiValidateTokenAdmResponse,
	PefilUsuarioAdm,
	UsuarioService,
} from "adm-legado-api";
import { map } from "rxjs/operators";
import { AdmCoreApiEmpresaService, Empresa } from "adm-core-api";
import {
	AdmMsApiCaixaService,
	AdmMsApiConfiguracaoService,
	Caixa,
	ConfiguracaoSistema,
} from "adm-ms-api";
import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecurso,
} from "treino-api";
import { PlataformModuleConfig } from "../models";
import { Subject } from "rxjs";
import {
	ConfiguracaoFinanceiro,
	FinanceiroMsApiConfiguracaoService,
} from "financeiro-ms-api";

@Injectable({
	providedIn: "root",
})
export class PermissaoService {
	public configuracaoEmpresaAdm: Empresa;
	public configuracaoSistemaAdm: ConfiguracaoSistema;
	public configuracaoFinanceiro: ConfiguracaoFinanceiro;
	public caixaEmAberto: Caixa;
	public permissaoUsaPrescricaoIA: boolean = false;
	private _configSistemaLoaded: Subject<void> = new Subject<void>();
	private _configEmpresaLoaded: Subject<void> = new Subject<void>();

	constructor(
		public usuarioService: UsuarioService,
		public empresaService: AdmCoreApiEmpresaService,
		public financeiroService: FinanceiroMsApiConfiguracaoService,
		public configuracaoSistemaService: AdmMsApiConfiguracaoService,
		public caixaService: AdmMsApiCaixaService,
		private pactoLayoutSDKWrapper: PactoLayoutSDKWrapper
	) {
		this.init();
	}

	private async init() {
		this.configuracaoEmpresaAdm = await this.obterConfiguracaoEmpresaAdm();
		this.configuracaoFinanceiro = await this.obterConfiguracaoFinanceiro();
		this._configEmpresaLoaded.next();
		this.configuracaoSistemaAdm = await this.obterConfiguracaoSistemaAdm();
		this._configSistemaLoaded.next();
		this.caixaEmAberto = await this.obterCaixaEmAberto();
	}

	temPerfilAdm() {
		const { perfilUsuarioAdm } = this.pactoLayoutSDKWrapper.sessionService;

		if (
			perfilUsuarioAdm &&
			perfilUsuarioAdm.perfilUsuario &&
			perfilUsuarioAdm.perfilUsuario.funcionalidades &&
			perfilUsuarioAdm.perfilUsuario.funcionalidades.length > 0
		) {
			return true;
		} else {
			return false;
		}
	}

	temConfiguracaoEmpresaAdm(configuracao: string) {
		let permitido = false;

		if (this.configuracaoEmpresaAdm) {
			permitido = Boolean(this.configuracaoEmpresaAdm[configuracao]);
		}

		return permitido;
	}

	temConfiguracaoSistemaAdm(configuracao: string) {
		let permitido = false;

		if (this.configuracaoSistemaAdm) {
			permitido = Boolean(this.configuracaoSistemaAdm[configuracao]);
		}

		return permitido;
	}

	temModuloHabilitado(siglaModulo: string): boolean {
		if (siglaModulo === null || siglaModulo === undefined) {
			return true;
		}

		let permissao = true;
		let siglaValidar = siglaModulo.trim().toUpperCase();

		if (siglaModulo === "NPES") {
			siglaValidar = PlataformModuleConfig.TREINO.sigla;
		}

		if (siglaValidar === PlataformModuleConfig.AGENDA.sigla) {
			siglaValidar = PlataformModuleConfig.TREINO.sigla;
		}

		if (siglaValidar === PlataformModuleConfig.PACTOPAY.sigla) {
			permissao = this.temPermissaoAdm("2.78");
		}

		if (siglaValidar === PlataformModuleConfig.NOTA_FISCAL.sigla) {
			return this.temPermissaoAdm("10.00");
		}

		if (siglaValidar === PlataformModuleConfig.CCL.sigla) {
			return true;
		}

		const { perfilUsuarioAdm } = this.pactoLayoutSDKWrapper.sessionService;

		const { integracaoZW } = this.pactoLayoutSDKWrapper.sessionService;

		if (
			integracaoZW &&
			!(
				perfilUsuarioAdm &&
				perfilUsuarioAdm.perfilUsuario &&
				perfilUsuarioAdm.perfilUsuario.funcionalidades
			)
		) {
			if (
				siglaModulo === "TR" ||
				siglaModulo === "NTR" ||
				siglaModulo === "NCR" ||
				siglaModulo === "NAV"
			) {
				return true;
			} else {
				return false;
			}
		}

		return (
			this.pactoLayoutSDKWrapper.sessionService.modulosHabilitados.includes(
				siglaValidar
			) && permissao
		);
	}

	temFuncionalidadeTreino(
		funcionalidadeNome: PerfilAcessoFuncionalidadeNome
	): boolean {
		let permitido = false;

		if (this.pactoLayoutSDKWrapper.sessionService.perfilUsuarioTreino) {
			if (
				this.pactoLayoutSDKWrapper.sessionService.perfilUsuarioTreino.funcionalidades.get(
					funcionalidadeNome
				)
			) {
				permitido = true;
			}
		}

		return permitido;
	}

	temPermissaoTreino(permissaoAcesso: PerfilAcessoRecurso): boolean {
		let permitido = false;

		if (this.pactoLayoutSDKWrapper.sessionService.perfilUsuarioTreino) {
			const recursoPerfilUsuario =
				this.pactoLayoutSDKWrapper.sessionService.perfilUsuarioTreino.recursos.get(
					permissaoAcesso.recurso
				);
			if (recursoPerfilUsuario) {
				if (
					!permissaoAcesso.permissao ||
					permissaoAcesso.permissao.length == 0
				) {
					permitido = true;
				} else {
					recursoPerfilUsuario.permissao.forEach((permissaoDoUsuario) => {
						const permissoesAcessoUsuarioEncontradas =
							permissaoAcesso.permissao.filter(
								(permissao) => permissao == permissaoDoUsuario
							);
						if (
							permissoesAcessoUsuarioEncontradas &&
							permissoesAcessoUsuarioEncontradas.length > 0
						) {
							permitido = true;
						}
					});
				}
			}
		}

		return permitido;
	}

	consultarPermissaoUsaPrescricaoIA(
		permissaoAcesso: PerfilAcessoRecurso
	): boolean {
		const temPermissaoAcesso = this.temPermissaoTreino(permissaoAcesso);
		this.permissaoUsaPrescricaoIA =
			this.pactoLayoutSDKWrapper.sessionService.usaPrescricaoIA;
		return temPermissaoAcesso && this.permissaoUsaPrescricaoIA;
	}

	recursoAdm(codigoPermissao: string) {
		const { perfilUsuarioAdm } = this.pactoLayoutSDKWrapper.sessionService;

		if (perfilUsuarioAdm && perfilUsuarioAdm.perfilUsuario) {
			const recurso = perfilUsuarioAdm.perfilUsuario.recursos.filter(
				(recurso) => {
					if (
						recurso &&
						recurso.referenciaRecurso &&
						recurso.referenciaRecurso.trim().toLowerCase() ===
							codigoPermissao.trim().toLowerCase()
					) {
						return true;
					}
				}
			);

			if (recurso && recurso.length > 0) {
				return recurso[0];
			}
		}

		return { tipoPermissoes: [] };
	}

	temRecursoAdm(codigoPermissao: string): boolean {
		let permitido = false;
		const { perfilUsuarioAdm } = this.pactoLayoutSDKWrapper.sessionService;

		if (perfilUsuarioAdm && perfilUsuarioAdm.perfilUsuario) {
			const recurso = perfilUsuarioAdm.perfilUsuario.recursos.filter(
				(recurso) => {
					if (
						recurso &&
						recurso.referenciaRecurso &&
						recurso.referenciaRecurso.trim().toLowerCase() ===
							codigoPermissao.trim().toLowerCase()
					) {
						return true;
					}
				}
			);

			if (recurso && recurso.length > 0) {
				permitido = true;
			}
		}

		return permitido;
	}

	temPermissaoAdm(codigoPermissao: string): boolean {
		let permitido = false;

		const { perfilUsuarioAdm } = this.pactoLayoutSDKWrapper.sessionService;

		if (
			perfilUsuarioAdm &&
			perfilUsuarioAdm.perfilUsuario &&
			perfilUsuarioAdm.perfilUsuario.funcionalidades
		) {
			const funcionalidade =
				perfilUsuarioAdm.perfilUsuario.funcionalidades.filter(
					(funcionaliade) => {
						if (
							funcionaliade &&
							funcionaliade.referenciaFuncionalidade &&
							funcionaliade.referenciaFuncionalidade.trim().toLowerCase() ===
								codigoPermissao.trim().toLowerCase()
						) {
							return true;
						}
					}
				);

			if (funcionalidade && funcionalidade.length > 0) {
				permitido = true;
			}
		}

		return permitido;
	}

	temCaixaEmAberto(): boolean {
		if (this.caixaEmAberto) {
			return true;
		} else {
			return false;
		}
	}

	private obterPermissoesUsuarioAdm(): Promise<PefilUsuarioAdm> {
		return this.usuarioService
			.permissoes(
				this.pactoLayoutSDKWrapper.sessionService.chave,
				this.pactoLayoutSDKWrapper.sessionService.codUsuarioZW,
				this.pactoLayoutSDKWrapper.sessionService.empresaId
			)
			.pipe(
				map((response: ApiValidateTokenAdmResponse) => {
					return response.content;
				})
			)
			.toPromise();
	}

	obterConfiguracaoEmpresaAdm(): Promise<Empresa> {
		return this.empresaService
			.find(this.pactoLayoutSDKWrapper.sessionService.empresaId)
			.pipe(
				map((response) => {
					return response.content;
				})
			)
			.toPromise();
	}

	get configuracaoEmpresaLoaded$() {
		return this._configEmpresaLoaded.asObservable();
	}

	obterConfiguracaoSistemaAdm(): Promise<ConfiguracaoSistema> {
		return this.configuracaoSistemaService
			.consultar()
			.pipe(
				map((configuracaoSistema) => {
					return configuracaoSistema;
				})
			)
			.toPromise();
	}

	/**
	 *  Necessário devido ao carregamento das configurações ainda não estarem carregados.
	 *  Ao finalizar a chamada o observable é atualizado e enviado para as subscrições onde
	 * pode realizar as verificações.
	 */
	get configuracaoSistemaLoaded$() {
		return this._configSistemaLoaded.asObservable();
	}

	obterCaixaEmAberto(): Promise<Caixa> {
		return this.caixaService
			.consultarCaixaEmAberto(
				this.pactoLayoutSDKWrapper.sessionService.empresaId
			)
			.pipe(
				map((caixa) => {
					return caixa;
				})
			)
			.toPromise();
	}

	atualizarCaixaEmAberto() {
		return this.caixaService
			.consultarCaixaEmAberto(
				this.pactoLayoutSDKWrapper.sessionService.empresaId
			)
			.subscribe((caixa) => {
				this.caixaEmAberto = caixa;
			});
	}

	usuarioPacto(): boolean {
		return (
			this.pactoLayoutSDKWrapper &&
			this.pactoLayoutSDKWrapper.sessionService &&
			this.pactoLayoutSDKWrapper.sessionService.loggedUser &&
			this.pactoLayoutSDKWrapper.sessionService.loggedUser.username &&
			(this.pactoLayoutSDKWrapper.sessionService.loggedUser.username.toLowerCase() ===
				"pactobr" ||
				this.pactoLayoutSDKWrapper.sessionService.loggedUser.username.toLowerCase() ===
					"admin")
		);
	}

	get empresaLogadoIsFranqueadora(): boolean {
		return (
			this.pactoLayoutSDKWrapper.sessionService &&
			this.pactoLayoutSDKWrapper.sessionService.configsTreinoRede &&
			this.pactoLayoutSDKWrapper.sessionService.configsTreinoRede
				.empresaLogadoIsFranqueadora
		);
	}

	get empresaLogadaNaoPossuiFranqueadora(): boolean {
		return (
			this.pactoLayoutSDKWrapper.sessionService &&
			this.pactoLayoutSDKWrapper.sessionService.configsTreinoRede &&
			this.pactoLayoutSDKWrapper.sessionService.configsTreinoRede
				.empresaLogadaNaoPossuiFranqueadora
		);
	}

	temModuloNotaFiscalHabilitado(siglaModulo: string): boolean {
		if (siglaModulo === null || siglaModulo === undefined) {
			return true;
		}

		let permissao = true;
		let siglaValidar = siglaModulo.trim().toUpperCase();

		if (siglaValidar === PlataformModuleConfig.NOTA_FISCAL.sigla) {
			let permissaoNotas = false;
			if (this.temPermissaoAdm("10.00") || this.temPermissaoAdm("7.57")) {
				permissaoNotas = true;
			}
			return permissaoNotas;
		}
	}

	obterConfiguracaoFinanceiro(): Promise<ConfiguracaoFinanceiro> {
		return this.financeiroService
			.consultar()
			.pipe(
				map((response) => {
					return response;
				})
			)
			.toPromise();
	}

	temConfiguracaoFinanceiro(configuracao: string) {
		let permitido = false;

		if (this.configuracaoFinanceiro) {
			permitido = Boolean(this.configuracaoFinanceiro[configuracao]);
		}

		return permitido;
	}
}
