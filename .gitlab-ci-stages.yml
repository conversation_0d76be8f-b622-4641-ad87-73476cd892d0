# YAML anchors for reusable configurations
.job_config: &job_config
  image: registry.gitlab.com/plataformazw/tag-versions:master
  interruptible: true
  tags:
    - docker
    - large
    - locaweb
  stage: finish
  allow_failure: true
  only:
    - master
  except:
    variables:
      - $DEPLOY_LOGIN_PROD
      - $DEPLOY_UI_PROD
      - $RELEASE_TAG

.git_setup: &git_setup
  - set +e                  # Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
  - eval $(ssh-agent -s)
  - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
  - git config --global user.name "Pipeline"
  - git config --global user.email "<EMAIL>"
  - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH


# Base template for merge jobs
.merge_template:
  <<: *job_config
  variables:
    CHAT_WEBHOOK_URL: "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
  script:
    - *git_setup
    - git reset --hard
    - git fetch
    - git checkout "$TARGET_BRANCH"
    - git pull origin "$TARGET_BRANCH"
    - PULL_RESULT=$(git pull origin master 2>&1)
    - |
      echo "=== DEBUG: RESULTADO DO PULL ==="
      echo "$PULL_RESULT"
      echo "================================"
      
      if echo "$PULL_RESULT" | grep -E -q "Automatic merge failed|would be overwritten"; then
        echo "Contém conflitos!!"
        curl -s --request POST --header "PRIVATE-TOKEN: $API_TOKEN" --data "source_branch=master&target_branch=$TARGET_BRANCH_ENCODED&title=$MR_TITLE" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests"
        CHAT_MESSAGE='{"text": "🚨 **CONFLITOS NO ZW_UI** 🚨\n\n📋 **Branch:** `'$TARGET_BRANCH'`\n💬 '$TEAM_MESSAGE'"}'
        curl -s --request POST --header "Content-Type: application/json" --data "$CHAT_MESSAGE" "$CHAT_WEBHOOK_URL"
        exit 1
      else
        echo $CI_COMMIT_REF_NAME
        echo "Não contém conflito"
        # Configurar upstream se necessário e fazer push
        GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push
      fi

# Job definitions with specific variables
zeta-stage_merge-develop:
  extends: .merge_template
  variables:
    TARGET_BRANCH: "develop"
    TARGET_BRANCH_ENCODED: "develop"
    MR_TITLE: "AutoMerge"
    TEAM_MESSAGE: "Povão do Develop, cadê vocês?"

omega-stage_merge-release:
  extends: .merge_template
  variables:
    TARGET_BRANCH: "release/RC"
    TARGET_BRANCH_ENCODED: "release%2FRC"
    MR_TITLE: "AutoMerge"
    TEAM_MESSAGE: "Galera da Release, cadê vocês?"

chi-stage_merge-gc:
  extends: .merge_template
  variables:
    TARGET_BRANCH: "release/RC-GC"
    TARGET_BRANCH_ENCODED: "release%2FRC-GC"
    MR_TITLE: "AutoMerge%20(Master_RC-GC)"
    TEAM_MESSAGE: "Guardião da branch GC, apresente-se!"

upsilon-stage_merge-manaus:
  extends: .merge_template
  variables:
    TARGET_BRANCH: "release/rc-manaus"
    TARGET_BRANCH_ENCODED: "release%2Frc-manaus"
    MR_TITLE: "AutoMerge%20(Master_RC-MANAUS)"
    TEAM_MESSAGE: "Guardião da branch rc-manaus, apresente-se!"