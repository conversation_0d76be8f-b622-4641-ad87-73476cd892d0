{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"plataforma-pacto": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/treino/pt", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "i18nLocale": "pt", "assets": ["src/assets", "src/favicon.ico", {"glob": "**", "input": "dist/ui-kit/assets", "output": "pacto-ui"}], "styles": ["node_modules/ngx-toastr/toastr.css", "src/styles.css", "src/assets/scss/style.scss", "src/assets/scss/animacoes.scss", "node_modules/dragula/dist/dragula.css", "dist/ui-kit/assets/ui-kit.scss", "dist/ui-kit/assets/scss/material-theme.scss", "./node_modules/quill/dist/quill.core.css", "./node_modules/quill/dist/quill.bubble.css", "./node_modules/quill/dist/quill.snow.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/apexcharts/dist/apexcharts.min.js", "node_modules/mousetrap/mousetrap.min.js", "node_modules/moment/moment.js", "node_modules/dragula/dist/dragula.js", "node_modules/moment/min/moment.min.js", "node_modules/moment/locale/pt-br.js", "./node_modules/quill/dist/quill.min.js"]}, "configurations": {"debug": {"outputPath": "dist/pt-debug/", "optimization": false, "sourceMap": true}, "es": {"aot": true, "outputPath": "dist/treino/es", "i18nFile": "src/i18n/messages.es.xlf", "i18nFormat": "xlf", "i18nLocale": "es"}, "en": {"aot": true, "outputPath": "dist/treino/en", "i18nFile": "src/i18n/messages.en.xlf", "i18nFormat": "xlf", "i18nLocale": "en"}, "development": {"tsConfig": "tsconfig.dev.json"}, "production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "plataforma-pacto:build"}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "plataforma-pacto:build", "optimization": false, "sourceMap": true, "vendorSourceMap": true}, "configurations": {"es": {"browserTarget": "plataforma-pacto:build:es"}, "en": {"browserTarget": "plataforma-pacto:build:en"}, "production": {"browserTarget": "plataforma-pacto:build:production"}, "development": {"browserTarget": "plataforma-pacto:build:development"}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": ["node_modules/jquery/dist/jquery.min.js"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.css", "src/assets/scss/style.scss"], "assets": ["src/assets", "src/favicon.ico"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**", "src/environments/**"]}}, "xliffmerge": {"builder": "@ngx-i18nsupport/tooling:xliffmerge", "options": {"xliffmergeOptions": {"i18nFormat": "xlf", "srcDir": "src/i18n", "genDir": "src/i18n", "defaultLanguage": "pt", "languages": ["pt", "es", "en"]}}}}}, "pacto-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "plataforma-pacto:serve"}, "configurations": {"production": {"devServerTarget": "plataforma-pacto:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.json", "exclude": ["**/node_modules/**"]}}}}, "old-ui-kit": {"root": "projects/old-ui-kit", "sourceRoot": "projects/old-ui-kit/src", "projectType": "library", "prefix": "old", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/old-ui-kit/tsconfig.lib.json", "project": "projects/old-ui-kit/ng-package.json"}}}}, "ui": {"root": "projects/ui", "sourceRoot": "projects/ui/src", "projectType": "library", "prefix": "pacto", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/ui/tsconfig.lib.json", "project": "projects/ui/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/ui/src/test.ts", "tsConfig": "projects/ui/tsconfig.spec.json", "karmaConfig": "projects/ui/karma.conf.js"}}}}, "login": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "prefix": "login"}}, "root": "projects/login", "sourceRoot": "projects/login/src", "prefix": "login", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/login/pt", "index": "projects/login/src/index.html", "main": "projects/login/src/main.ts", "polyfills": "projects/login/src/polyfills.ts", "tsConfig": "projects/login/tsconfig.app.json", "aot": false, "assets": ["projects/login/src/favicon.ico", "projects/login/src/assets", {"glob": "**", "input": "dist/ui-kit/assets", "output": "pacto-ui"}], "styles": ["node_modules/quill/dist/quill.core.css", "node_modules/quill/dist/quill.bubble.css", "node_modules/ng-snotify/styles/material.css", "projects/login/src/styles.scss", "node_modules/dragula/dist/dragula.css", "dist/ui-kit/assets/ui-kit.scss", "dist/ui-kit/assets/scss/material-theme.scss", "node_modules/quill/dist/quill.snow.css", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/apexcharts/dist/apexcharts.min.js", "node_modules/mousetrap/mousetrap.min.js", "node_modules/moment/moment.js", "node_modules/dragula/dist/dragula.js", "node_modules/moment/min/moment.min.js", "node_modules/moment/locale/pt-br.js", "node_modules/quill/dist/quill.js"]}, "configurations": {"es": {"aot": true, "outputPath": "dist/login/es", "i18nFile": "projects/login/i18n/messages.es.xlf", "i18nFormat": "xlf", "i18nLocale": "es"}, "en": {"aot": true, "outputPath": "dist/login/en", "i18nFile": "projects/login/i18n/messages.en.xlf", "i18nFormat": "xlf", "i18nLocale": "en"}, "production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "debug": {"outputPath": "dist/login-debug/", "optimization": false, "sourceMap": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "login:build", "vendorSourceMap": true}, "configurations": {"production": {"browserTarget": "login:build:production"}, "es": {"browserTarget": "login:build:es"}, "en": {"browserTarget": "login:build:en"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "login:build"}}, "xliffmerge": {"builder": "@ngx-i18nsupport/tooling:xliffmerge", "options": {"xliffmergeOptions": {"i18nFormat": "xlf", "srcDir": "projects/login/i18n", "genDir": "projects/login/i18n", "defaultLanguage": "pt", "languages": ["pt", "es", "en"]}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/login/src/test.ts", "polyfills": "projects/login/src/polyfills.ts", "tsConfig": "projects/login/tsconfig.spec.json", "karmaConfig": "projects/login/karma.conf.js", "assets": ["projects/login/src/favicon.ico", "projects/login/src/assets"], "styles": ["projects/login/src/styles.scss"], "scripts": []}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "projects/login/e2e/protractor.conf.js", "devServerTarget": "login:serve"}, "configurations": {"production": {"devServerTarget": "login:serve:production"}}}}}, "canal-cliente": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "changeDetection": "OnPush", "spec": false, "prefix": "ccl"}}, "root": "projects/canal-cliente", "sourceRoot": "projects/canal-cliente/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/canal-cliente/pt", "index": "projects/canal-cliente/src/index.html", "main": "projects/canal-cliente/src/main.ts", "polyfills": "projects/canal-cliente/src/polyfills.ts", "tsConfig": "projects/canal-cliente/tsconfig.app.json", "aot": false, "assets": ["projects/canal-cliente/src/favicon.ico", "projects/canal-cliente/src/assets", {"glob": "**", "input": "dist/ui-kit/assets", "output": "pacto-ui"}], "styles": ["projects/canal-cliente/src/styles.scss", "dist/ui-kit/assets/ui-kit.scss", "dist/ui-kit/assets/scss/material-theme.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/moment/min/moment.min.js", "node_modules/apexcharts/dist/apexcharts.min.js"]}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": true, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "canal-cliente:build", "vendorSourceMap": true}, "configurations": {"production": {"browserTarget": "canal-cliente:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "canal-cliente:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/canal-cliente/src/test.ts", "polyfills": "projects/canal-cliente/src/polyfills.ts", "tsConfig": "projects/canal-cliente/tsconfig.spec.json", "karmaConfig": "projects/canal-cliente/karma.conf.js", "assets": ["projects/canal-cliente/src/favicon.ico", "projects/canal-cliente/src/assets"], "styles": ["projects/canal-cliente/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/canal-cliente/tsconfig.app.json", "projects/canal-cliente/tsconfig.spec.json", "projects/canal-cliente/e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "projects/canal-cliente/e2e/protractor.conf.js", "devServerTarget": "canal-cliente:serve"}, "configurations": {"production": {"devServerTarget": "canal-cliente:serve:production"}}}}}, "sandbox": {"root": "projects/sandbox/", "sourceRoot": "projects/sandbox/src", "projectType": "application", "prefix": "app", "schematics": {}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/sandbox", "index": "projects/sandbox/src/index.html", "main": "projects/sandbox/src/main.ts", "polyfills": "projects/sandbox/src/polyfills.ts", "tsConfig": "projects/sandbox/tsconfig.app.json", "styles": ["projects/ui/assets/ui-kit.scss", "projects/ui/assets/scss/material-theme.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/moment/min/moment.min.js", "node_modules/apexcharts/dist/apexcharts.min.js"], "assets": ["projects/sandbox/src/favicon.ico", "projects/sandbox/src/assets", {"glob": "**", "input": "projects/ui/assets", "output": "pacto-ui"}], "es5BrowserSupport": true}, "configurations": {"production": {"fileReplacements": [{"replace": "projects/sandbox/src/environments/environment.ts", "with": "projects/sandbox/src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": true, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "sandbox:build", "optimization": false, "sourceMap": true, "vendorSourceMap": true}, "configurations": {"production": {"browserTarget": "sandbox:build:production"}}}}}, "adm": {"projectType": "library", "schematics": {"@schematics/angular:component": {"style": "scss", "prefix": "adm"}}, "root": "projects/adm", "sourceRoot": "projects/adm/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/adm/pt", "index": "projects/adm/src/index.html", "main": "projects/adm/src/main.ts", "polyfills": "projects/adm/src/polyfills.ts", "tsConfig": "projects/adm/tsconfig.app.json", "aot": false, "assets": ["projects/adm/src/favicon.ico", "projects/adm/src/assets", {"glob": "**", "input": "dist/ui-kit/assets", "output": "pacto-ui"}], "styles": ["node_modules/ngx-toastr/toastr.css", "node_modules/ng-snotify/styles/material.css", "projects/adm/src/styles.scss", "dist/ui-kit/assets/ui-kit.scss", "dist/ui-kit/assets/scss/material-theme.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/moment/min/moment.min.js", "node_modules/apexcharts/dist/apexcharts.min.js"]}, "configurations": {"es": {"aot": true, "outputPath": "dist/adm/es", "i18nFile": "projects/adm/i18n/messages.es.xlf", "i18nFormat": "xlf", "i18nLocale": "es"}, "en": {"aot": true, "outputPath": "dist/adm/en", "i18nFile": "projects/adm/i18n/messages.en.xlf", "i18nFormat": "xlf", "i18nLocale": "en"}, "production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"vendorSourceMap": true, "browserTarget": "adm:build"}, "configurations": {"production": {"browserTarget": "adm:build:production"}, "es": {"browserTarget": "adm:build:es"}, "en": {"browserTarget": "adm:build:en"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "adm:build"}}, "xliffmerge": {"builder": "@ngx-i18nsupport/tooling:xliffmerge", "options": {"xliffmergeOptions": {"i18nFormat": "xlf", "srcDir": "projects/adm/i18n", "genDir": "projects/adm/i18n", "defaultLanguage": "pt", "languages": ["pt", "es", "en"]}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/adm/src/test.ts", "polyfills": "projects/adm/src/polyfills.ts", "tsConfig": "projects/adm/tsconfig.spec.json", "karmaConfig": "projects/adm/karma.conf.js", "assets": ["projects/adm/src/favicon.ico", "projects/adm/src/assets"], "styles": ["projects/adm/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/adm/tsconfig.app.json", "projects/adm/tsconfig.spec.json", "projects/adm/e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "projects/adm/e2e/protractor.conf.js", "devServerTarget": "adm:serve"}, "configurations": {"production": {"devServerTarget": "adm:serve:production"}}}}}, "sdk": {"projectType": "library", "root": "projects/sdk", "sourceRoot": "projects/sdk/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/sdk/tsconfig.lib.json", "project": "projects/sdk/ng-package.json"}, "configurations": {"production": {}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/sdk/src/test.ts", "tsConfig": "projects/sdk/tsconfig.spec.json", "karmaConfig": "projects/sdk/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/sdk/tsconfig.lib.json", "projects/sdk/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "ui-sandbox": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/ui-sandbox", "sourceRoot": "projects/ui-sandbox/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/ui-sandbox", "index": "projects/ui-sandbox/src/index.html", "main": "projects/ui-sandbox/src/main.ts", "polyfills": "projects/ui-sandbox/src/polyfills.ts", "tsConfig": "projects/ui-sandbox/tsconfig.app.json", "aot": false, "assets": ["projects/ui-sandbox/src/favicon.ico", "projects/ui-sandbox/src/assets"], "styles": ["projects/ui-sandbox/src/styles.scss"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/moment/min/moment.min.js", "node_modules/apexcharts/dist/apexcharts.min.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "projects/ui-sandbox/src/environments/environment.ts", "with": "projects/ui-sandbox/src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"vendorSourceMap": true, "browserTarget": "ui-sandbox:build"}, "configurations": {"production": {"browserTarget": "ui-sandbox:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ui-sandbox:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/ui-sandbox/src/test.ts", "polyfills": "projects/ui-sandbox/src/polyfills.ts", "tsConfig": "projects/ui-sandbox/tsconfig.spec.json", "karmaConfig": "projects/ui-sandbox/karma.conf.js", "assets": ["projects/ui-sandbox/src/favicon.ico", "projects/ui-sandbox/src/assets"], "styles": ["projects/ui-sandbox/src/styles.scss", "dist/ui-kit/assets/ui-kit.scss", "dist/ui-kit/assets/scss/material-theme.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/ui-sandbox/tsconfig.app.json", "projects/ui-sandbox/tsconfig.spec.json", "projects/ui-sandbox/e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "projects/ui-sandbox/e2e/protractor.conf.js", "devServerTarget": "ui-sandbox:serve"}, "configurations": {"production": {"devServerTarget": "ui-sandbox:serve:production"}}}}}, "treino-api": {"projectType": "library", "root": "projects/treino-api", "sourceRoot": "projects/treino-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/treino-api/tsconfig.lib.json", "project": "projects/treino-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/treino-api/src/test.ts", "tsConfig": "projects/treino-api/tsconfig.spec.json", "karmaConfig": "projects/treino-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/treino-api/tsconfig.lib.json", "projects/treino-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "zw-pactopay-api": {"projectType": "library", "root": "projects/zw-pactopay-api", "sourceRoot": "projects/zw-pactopay-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/zw-pactopay-api/tsconfig.lib.json", "project": "projects/zw-pactopay-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/zw-pactopay-api/src/test.ts", "tsConfig": "projects/zw-pactopay-api/tsconfig.spec.json", "karmaConfig": "projects/zw-pactopay-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/zw-pactopay-api/tsconfig.lib.json", "projects/zw-pactopay-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "zw-servlet-api": {"projectType": "library", "root": "projects/zw-servlet-api", "sourceRoot": "projects/zw-servlet-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/zw-servlet-api/tsconfig.lib.json", "project": "projects/zw-servlet-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/zw-servlet-api/src/test.ts", "tsConfig": "projects/zw-servlet-api/tsconfig.spec.json", "karmaConfig": "projects/zw-servlet-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/zw-servlet-api/tsconfig.lib.json", "projects/zw-servlet-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "cadastro-aux-api": {"projectType": "library", "root": "projects/cadastro-aux-api", "sourceRoot": "projects/cadastro-aux-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/cadastro-aux-api/tsconfig.lib.json", "project": "projects/cadastro-aux-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/cadastro-aux-api/src/test.ts", "tsConfig": "projects/cadastro-aux-api/tsconfig.spec.json", "karmaConfig": "projects/cadastro-aux-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/cadastro-aux-api/tsconfig.lib.json", "projects/cadastro-aux-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "plano-api": {"projectType": "library", "root": "projects/plano-api", "sourceRoot": "projects/plano-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/plano-api/tsconfig.lib.json", "project": "projects/plano-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/plano-api/src/test.ts", "tsConfig": "projects/plano-api/tsconfig.spec.json", "karmaConfig": "projects/plano-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/plano-api/tsconfig.lib.json", "projects/plano-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "acesso-sistema-api": {"projectType": "library", "root": "projects/acesso-sistema-api", "sourceRoot": "projects/acesso-sistema-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/acesso-sistema-api/tsconfig.lib.json", "project": "projects/acesso-sistema-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/acesso-sistema-api/src/test.ts", "tsConfig": "projects/acesso-sistema-api/tsconfig.spec.json", "karmaConfig": "projects/acesso-sistema-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/acesso-sistema-api/tsconfig.lib.json", "projects/acesso-sistema-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "adm-core-api": {"projectType": "library", "root": "projects/adm-core-api", "sourceRoot": "projects/adm-core-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/adm-core-api/tsconfig.lib.json", "project": "projects/adm-core-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/adm-core-api/src/test.ts", "tsConfig": "projects/adm-core-api/tsconfig.spec.json", "karmaConfig": "projects/adm-core-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/adm-core-api/tsconfig.lib.json", "projects/adm-core-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "pactopay-api": {"projectType": "library", "root": "projects/pactopay-api", "sourceRoot": "projects/pactopay-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/pactopay-api/tsconfig.lib.json", "project": "projects/pactopay-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/pactopay-api/src/test.ts", "tsConfig": "projects/pactopay-api/tsconfig.spec.json", "karmaConfig": "projects/pactopay-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/pactopay-api/tsconfig.lib.json", "projects/pactopay-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "clube-vantagens-api": {"projectType": "library", "root": "projects/clube-vantagens-api", "sourceRoot": "projects/clube-vantagens-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/clube-vantagens-api/tsconfig.lib.json", "project": "projects/clube-vantagens-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/clube-vantagens-api/src/test.ts", "tsConfig": "projects/clube-vantagens-api/tsconfig.spec.json", "karmaConfig": "projects/clube-vantagens-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/clube-vantagens-api/tsconfig.lib.json", "projects/clube-vantagens-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "produto-api": {"projectType": "library", "root": "projects/produto-api", "sourceRoot": "projects/produto-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/produto-api/tsconfig.lib.json", "project": "projects/produto-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/produto-api/src/test.ts", "tsConfig": "projects/produto-api/tsconfig.spec.json", "karmaConfig": "projects/produto-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/produto-api/tsconfig.lib.json", "projects/produto-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "relatorio-api": {"projectType": "library", "root": "projects/relatorio-api", "sourceRoot": "projects/relatorio-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/relatorio-api/tsconfig.lib.json", "project": "projects/relatorio-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/relatorio-api/src/test.ts", "tsConfig": "projects/relatorio-api/tsconfig.spec.json", "karmaConfig": "projects/relatorio-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/relatorio-api/tsconfig.lib.json", "projects/relatorio-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "login-app-api": {"projectType": "library", "root": "projects/login-app-api", "sourceRoot": "projects/login-app-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/login-app-api/tsconfig.lib.json", "project": "projects/login-app-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/login-app-api/src/test.ts", "tsConfig": "projects/login-app-api/tsconfig.spec.json", "karmaConfig": "projects/login-app-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/login-app-api/tsconfig.lib.json", "projects/login-app-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "ms-pactopay-api": {"projectType": "library", "root": "projects/ms-pactopay-api", "sourceRoot": "projects/ms-pactopay-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/ms-pactopay-api/tsconfig.lib.json", "project": "projects/ms-pactopay-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/ms-pactopay-api/src/test.ts", "tsConfig": "projects/ms-pactopay-api/tsconfig.spec.json", "karmaConfig": "projects/ms-pactopay-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/ms-pactopay-api/tsconfig.lib.json", "projects/ms-pactopay-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "crm-api": {"projectType": "library", "root": "projects/crm-api", "sourceRoot": "projects/crm-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/crm-api/tsconfig.lib.json", "project": "projects/crm-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/crm-api/src/test.ts", "tsConfig": "projects/crm-api/tsconfig.spec.json", "karmaConfig": "projects/crm-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/crm-api/tsconfig.lib.json", "projects/crm-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "pacto-api": {"projectType": "library", "root": "projects/pacto-api", "sourceRoot": "projects/pacto-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/pacto-api/tsconfig.lib.json", "project": "projects/pacto-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/pacto-api/src/test.ts", "tsConfig": "projects/pacto-api/tsconfig.spec.json", "karmaConfig": "projects/pacto-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/pacto-api/tsconfig.lib.json", "projects/pacto-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "crm": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "prefix": "crm"}}, "root": "projects/crm", "sourceRoot": "projects/crm/src", "prefix": "crm", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/crm/pt", "index": "projects/crm/src/index.html", "main": "projects/crm/src/main.ts", "polyfills": "projects/crm/src/polyfills.ts", "tsConfig": "projects/crm/tsconfig.app.json", "aot": false, "assets": ["projects/crm/src/favicon.ico", "projects/crm/src/assets", {"glob": "**", "input": "dist/ui-kit/assets", "output": "pacto-ui"}], "styles": ["node_modules/ng-snotify/styles/material.css", "projects/adm/src/styles.scss", "dist/ui-kit/assets/ui-kit.scss", "dist/ui-kit/assets/scss/material-theme.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/moment/min/moment.min.js", "node_modules/apexcharts/dist/apexcharts.min.js"]}, "configurations": {"es": {"aot": true, "outputPath": "dist/crm/es", "i18nFile": "projects/crm/i18n/messages.es.xlf", "i18nFormat": "xlf", "i18nLocale": "es"}, "en": {"aot": true, "outputPath": "dist/crm/en", "i18nFile": "projects/crm/i18n/messages.en.xlf", "i18nFormat": "xlf", "i18nLocale": "en"}, "production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"vendorSourceMap": true, "browserTarget": "crm:build"}, "configurations": {"production": {"browserTarget": "crm:build:production"}, "es": {"browserTarget": "adm:build:es"}, "en": {"browserTarget": "adm:build:en"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "crm:build"}}, "xliffmerge": {"builder": "@ngx-i18nsupport/tooling:xliffmerge", "options": {"xliffmergeOptions": {"i18nFormat": "xlf", "srcDir": "projects/crm/i18n", "genDir": "projects/crm/i18n", "defaultLanguage": "pt", "languages": ["pt", "es", "en"]}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/crm/src/test.ts", "polyfills": "projects/crm/src/polyfills.ts", "tsConfig": "projects/crm/tsconfig.spec.json", "karmaConfig": "projects/crm/karma.conf.js", "assets": ["projects/crm/src/favicon.ico", "projects/crm/src/assets"], "styles": ["projects/crm/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/crm/tsconfig.app.json", "projects/crm/tsconfig.spec.json", "projects/crm/e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "projects/crm/e2e/protractor.conf.js", "devServerTarget": "crm:serve"}, "configurations": {"production": {"devServerTarget": "crm:serve:production"}}}}}, "midia-social-api": {"projectType": "library", "root": "projects/midia-social-api", "sourceRoot": "projects/midia-social-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/midia-social-api/tsconfig.lib.json", "project": "projects/midia-social-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/midia-social-api/src/test.ts", "tsConfig": "projects/midia-social-api/tsconfig.spec.json", "karmaConfig": "projects/midia-social-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/midia-social-api/tsconfig.lib.json", "projects/midia-social-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "adm-ms-api": {"projectType": "library", "root": "projects/adm-ms-api", "sourceRoot": "projects/adm-ms-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/adm-ms-api/tsconfig.lib.json", "project": "projects/adm-ms-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/adm-ms-api/src/test.ts", "tsConfig": "projects/adm-ms-api/tsconfig.spec.json", "karmaConfig": "projects/adm-ms-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/adm-ms-api/tsconfig.lib.json", "projects/adm-ms-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "pacto-layout": {"projectType": "library", "root": "projects/pacto-layout", "sourceRoot": "projects/pacto-layout/src", "prefix": "pacto", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/pacto-layout/tsconfig.lib.json", "project": "projects/pacto-layout/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/pacto-layout/src/test.ts", "tsConfig": "projects/pacto-layout/tsconfig.spec.json", "karmaConfig": "projects/pacto-layout/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/pacto-layout/tsconfig.lib.json", "projects/pacto-layout/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "adm-legado-api": {"projectType": "library", "root": "projects/adm-legado-api", "sourceRoot": "projects/adm-legado-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/adm-legado-api/tsconfig.lib.json", "project": "projects/adm-legado-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/adm-legado-api/src/test.ts", "tsConfig": "projects/adm-legado-api/tsconfig.spec.json", "karmaConfig": "projects/adm-legado-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/adm-legado-api/tsconfig.lib.json", "projects/adm-legado-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "marketing-api": {"projectType": "library", "root": "projects/marketing-api", "sourceRoot": "projects/marketing-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/marketing-api/tsconfig.lib.json", "project": "projects/marketing-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/marketing-api/src/test.ts", "tsConfig": "projects/marketing-api/tsconfig.spec.json", "karmaConfig": "projects/marketing-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/marketing-api/tsconfig.lib.json", "projects/marketing-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "marketing": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "prefix": "mkt"}}, "root": "projects/marketing", "sourceRoot": "projects/marketing/src", "prefix": "mkt", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/marketing", "index": "projects/marketing/src/index.html", "main": "projects/marketing/src/main.ts", "polyfills": "projects/marketing/src/polyfills.ts", "tsConfig": "projects/marketing/tsconfig.app.json", "aot": false, "assets": ["projects/marketing/src/favicon.ico", "projects/marketing/src/assets", {"glob": "**", "input": "dist/ui-kit/assets", "output": "pacto-ui"}], "styles": ["node_modules/ng-snotify/styles/material.css", "projects/marketing/src/styles.scss", "dist/ui-kit/assets/ui-kit.scss", "dist/ui-kit/assets/scss/material-theme.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/moment/min/moment.min.js", "node_modules/apexcharts/dist/apexcharts.min.js"]}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "6mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "marketing:build"}, "configurations": {"production": {"browserTarget": "marketing:build:production"}, "es": {"browserTarget": "marketing:build:es"}, "en": {"browserTarget": "marketing:build:en"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "marketing:build"}}, "xliffmerge": {"builder": "@ngx-i18nsupport/tooling:xliffmerge", "options": {"xliffmergeOptions": {"i18nFormat": "xlf", "srcDir": "projects/marketing/i18n", "genDir": "projects/marketing/i18n", "defaultLanguage": "pt", "languages": ["pt", "es", "en"]}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/marketing/src/test.ts", "polyfills": "projects/marketing/src/polyfills.ts", "tsConfig": "projects/marketing/tsconfig.spec.json", "karmaConfig": "projects/marketing/karma.conf.js", "assets": ["projects/marketing/src/favicon.ico", "projects/marketing/src/assets"], "styles": ["projects/marketing/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/marketing/tsconfig.app.json", "projects/marketing/tsconfig.spec.json", "projects/marketing/e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "projects/marketing/e2e/protractor.conf.js", "devServerTarget": "marketing:serve"}, "configurations": {"production": {"devServerTarget": "marketing:serve:production"}}}}}, "integracao-gympass-api": {"projectType": "library", "root": "projects/integracao-gympass-api", "sourceRoot": "projects/integracao-gympass-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/integracao-gympass-api/tsconfig.lib.json", "project": "projects/integracao-gympass-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/integracao-gympass-api/src/test.ts", "tsConfig": "projects/integracao-gympass-api/tsconfig.spec.json", "karmaConfig": "projects/integracao-gympass-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/integracao-gympass-api/tsconfig.lib.json", "projects/integracao-gympass-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "notificacao-api": {"projectType": "library", "root": "projects/notificacao-api", "sourceRoot": "projects/notificacao-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/notificacao-api/tsconfig.lib.json", "project": "projects/notificacao-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/notificacao-api/src/test.ts", "tsConfig": "projects/notificacao-api/tsconfig.spec.json", "karmaConfig": "projects/notificacao-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/notificacao-api/tsconfig.lib.json", "projects/notificacao-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "financeiro-ms-api": {"projectType": "library", "root": "projects/financeiro-ms-api", "sourceRoot": "projects/financeiro-ms-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/financeiro-ms-api/tsconfig.lib.json", "project": "projects/financeiro-ms-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/financeiro-ms-api/src/test.ts", "tsConfig": "projects/financeiro-ms-api/tsconfig.spec.json", "karmaConfig": "projects/financeiro-ms-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/financeiro-ms-api/tsconfig.lib.json", "projects/financeiro-ms-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "pessoa-ms-api": {"projectType": "library", "root": "projects/pessoa-ms-api", "sourceRoot": "projects/pessoa-ms-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/pessoa-ms-api/tsconfig.lib.json", "project": "projects/pessoa-ms-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/pessoa-ms-api/src/test.ts", "tsConfig": "projects/pessoa-ms-api/tsconfig.spec.json", "karmaConfig": "projects/pessoa-ms-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/pessoa-ms-api/tsconfig.lib.json", "projects/pessoa-ms-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "nichos": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/nichos", "sourceRoot": "projects/nichos/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/nichos", "index": "projects/nichos/src/index.html", "main": "projects/nichos/src/main.ts", "polyfills": "projects/nichos/src/polyfills.ts", "tsConfig": "projects/nichos/tsconfig.app.json", "aot": false, "assets": ["projects/nichos/src/favicon.ico", "projects/nichos/src/assets", {"glob": "**", "input": "dist/ui-kit/assets", "output": "pacto-ui"}], "styles": ["node_modules/ng-snotify/styles/material.css", "projects/nichos/src/styles.scss", "dist/ui-kit/assets/ui-kit.scss", "dist/ui-kit/assets/scss/material-theme.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "projects/nichos/src/environments/environment.ts", "with": "projects/nichos/src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "nichos:build", "optimization": false, "sourceMap": true, "vendorSourceMap": true}, "configurations": {"production": {"browserTarget": "nichos:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "nichos:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/nichos/src/test.ts", "polyfills": "projects/nichos/src/polyfills.ts", "tsConfig": "projects/nichos/tsconfig.spec.json", "karmaConfig": "projects/nichos/karma.conf.js", "assets": ["projects/nichos/src/favicon.ico", "projects/nichos/src/assets"], "styles": ["projects/nichos/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/nichos/tsconfig.app.json", "projects/nichos/tsconfig.spec.json", "projects/nichos/e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "projects/nichos/e2e/protractor.conf.js", "devServerTarget": "nichos:serve"}, "configurations": {"production": {"devServerTarget": "nichos:serve:production"}}}}}, "recurso-ms-api": {"projectType": "library", "root": "projects/recurso-ms-api", "sourceRoot": "projects/recurso-ms-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/recurso-ms-api/tsconfig.lib.json", "project": "projects/recurso-ms-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/recurso-ms-api/src/test.ts", "tsConfig": "projects/recurso-ms-api/tsconfig.spec.json", "karmaConfig": "projects/recurso-ms-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/recurso-ms-api/tsconfig.lib.json", "projects/recurso-ms-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "autenticacao-api": {"projectType": "library", "root": "projects/autenticacao-api", "sourceRoot": "projects/autenticacao-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/autenticacao-api/tsconfig.lib.json", "project": "projects/autenticacao-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/autenticacao-api/src/test.ts", "tsConfig": "projects/autenticacao-api/tsconfig.spec.json", "karmaConfig": "projects/autenticacao-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/autenticacao-api/tsconfig.lib.json", "projects/autenticacao-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "bi-ms-api": {"projectType": "library", "root": "projects/bi-ms-api", "sourceRoot": "projects/bi-ms-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-ng-packagr:build", "options": {"tsConfig": "projects/bi-ms-api/tsconfig.lib.json", "project": "projects/bi-ms-api/ng-package.json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/bi-ms-api/src/test.ts", "tsConfig": "projects/bi-ms-api/tsconfig.spec.json", "karmaConfig": "projects/bi-ms-api/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/bi-ms-api/tsconfig.lib.json", "projects/bi-ms-api/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "plataforma-pacto", "schematics": {"@schematics/angular:component": {"prefix": "pacto", "styleext": "scss"}, "@schematics/angular:directive": {"prefix": "pacto"}}}