stages:
  - install
  - build
  - build-docker
  - tag
  - deploy

variables:
  IMAGE: "registry.gitlab.com/plataformazw/zw_ui:$CI_COMMIT_REF_SLUG"

install-dependencies:
  image: node:12
  tags:
    - docker
    - large
  variables:
    ENABLE_LOGIN_CAPTCHA: 'false'
  only:
    - merge_requests
    - master
  stage: install
  artifacts:
    paths:
      - dist/
      - node_modules/
  script:
    - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
    - yarn build-libs 2> >(grep -v warning 1>&2)

build-treino:
  image: node:12
  dependencies:
    - install-dependencies
  tags:
    - docker
    - large
  variables:
    ENABLE_LOGIN_CAPTCHA: 'false'
  only:
    - merge_requests
    - master
  stage: build
  artifacts:
    paths:
      - dist/
      - node_modules/
  script:
    - yarn build-treino-all 2> >(grep -v warning 1>&2)

build-login:
 image: node:12
 dependencies:
   - install-dependencies
 tags:
   - docker
   - large
 variables:
   ENABLE_LOGIN_CAPTCHA: 'false'
 only:
   - merge_requests
   - master
 stage: build
 artifacts:
   paths:
     - dist/
     - node_modules/
 script:
   - yarn build-login-all 2> >(grep -v warning 1>&2)

build-adm:
 image: node:12
 dependencies:
   - install-dependencies
 tags:
   - docker
   - large
 variables:
   ENABLE_LOGIN_CAPTCHA: 'false'
 only:
   - merge_requests
   - master
 stage: build
 artifacts:
   paths:
     - dist/
     - node_modules/
 script:
   - yarn build-adm-all 2> >(grep -v warning 1>&2)

build-crm:
 image: node:12
 dependencies:
   - install-dependencies
 tags:
   - docker
   - large
 variables:
   ENABLE_LOGIN_CAPTCHA: 'false'
 only:
   - merge_requests
   - master
 stage: build
 artifacts:
   paths:
     - dist/
     - node_modules/
 script:
   - yarn build-crm-all 2> >(grep -v warning 1>&2)

build-canal-cliente:
 image: node:12
 dependencies:
   - install-dependencies
 tags:
   - docker
   - large
 variables:
   ENABLE_LOGIN_CAPTCHA: 'false'
 only:
   - merge_requests
   - master
 stage: build
 artifacts:
   paths:
     - dist/
     - node_modules/
 script:
   - yarn build-canal-cliente 2> >(grep -v warning 1>&2)

docker-build:  
  variables:
    DOCKER_TLS_CERTDIR: ""  
  dependencies:
    - build-treino
    - build-login
    - build-adm
    - build-crm
    - build-canal-cliente
  tags:
   - locaweb
   - swarm
  only:
    - merge_requests
    - master
  stage: build-docker
  artifacts:
    paths:
      - dist/
  script:
    - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
    - docker build -t $IMAGE .

tag-version:
  image: keviocastro/node-build
  stage: tag
  when: on_success
  except:
    variables:
      - $CI_COMMIT_MESSAGE =~ /Bump version:/    
  only:
    - master
  script:
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
    - npm version patch -m 'Bump version:%s'
    - git tag -f latest -m "Deploy tag"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH -f --tags
