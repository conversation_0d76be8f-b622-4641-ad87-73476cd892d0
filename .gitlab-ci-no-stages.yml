variables:
  IMAGE: "registry.gitlab.com/plataformazw/zw_ui:$CI_COMMIT_REF_SLUG"

build-treino:
  image: node:12
  tags:
    - docker
    - large
  variables:
    ENABLE_LOGIN_CAPTCHA: 'false'
  only:
    - merge_requests
    - master
  artifacts:
    paths:
      - dist/treino
  script:
    - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
    - yarn build-libs 2> >(grep -v warning 1>&2)
    - yarn build-treino-all 2> >(grep -v warning 1>&2)

build-login:
 image: node:12
 tags:
   - docker
   - large
 variables:
   ENABLE_LOGIN_CAPTCHA: 'false'
 only:
   - merge_requests
   - master
 artifacts:
   paths:
     - dist/login
 script:
  - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
  - yarn build-libs 2> >(grep -v warning 1>&2)
  - yarn build-login-all 2> >(grep -v warning 1>&2)

build-adm:
 image: node:12
 tags:
   - docker
   - large
 variables:
   ENABLE_LOGIN_CAPTCHA: 'false'
 only:
   - merge_requests
   - master
 artifacts:
   paths:
     - dist/adm
 script:
  - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
  - yarn build-libs 2> >(grep -v warning 1>&2)
  - yarn build-adm-all 2> >(grep -v warning 1>&2)

build-crm:
 image: node:12
 tags:
   - docker
   - large
 variables:
   ENABLE_LOGIN_CAPTCHA: 'false'
 only:
   - merge_requests
   - master
 artifacts:
   paths:
     - dist/crm
 script:
  - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
  - yarn build-libs 2> >(grep -v warning 1>&2)
  - yarn build-crm-all 2> >(grep -v warning 1>&2)

build-canal-cliente:
 image: node:12
 tags:
   - docker
   - large
 variables:
   ENABLE_LOGIN_CAPTCHA: 'false'
 only:
   - merge_requests
   - master
 artifacts:
   paths:
     - dist/canal-cliente
 script:
  - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
  - yarn build-libs 2> >(grep -v warning 1>&2)
  - yarn build-canal-cliente 2> >(grep -v warning 1>&2)

.docker-build: &docker-build-template  
  variables:
    DOCKER_TLS_CERTDIR: ""
  tags:
   - locaweb
   - swarm
  only:
    - merge_requests
    - master
  script:
    - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
    - docker build -t $IMAGE --build-arg app_dir=$APP_DIR .
    - docker push $IMAGE

docker-build-treino:  
  extends: .docker-build
  dependencies:
    - build-treino
  needs:
    - build-treino
  artifacts:
    paths:
      - dist/treino
  variables:
    IMAGE: "registry.gitlab.com/plataformazw/zw_ui/novo-treino:$CI_COMMIT_REF_SLUG"
    APP_DIR: "treino"

docker-build-canal-cliente:  
  extends: .docker-build
  needs:
    - build-canal-cliente
  dependencies:
    - build-canal-cliente
  artifacts:
    paths:
      - dist/canal-cliente
  variables:
    IMAGE: "registry.gitlab.com/plataformazw/zw_ui/canal-cliente:$CI_COMMIT_REF_SLUG"
    APP_DIR: "canal-cliente"

docker-build-adm:  
  extends: .docker-build
  dependencies:
    - build-adm
  needs:
    - build-adm
  artifacts:
    paths:
      - dist/adm
  variables:
    IMAGE: "registry.gitlab.com/plataformazw/zw_ui/adm:$CI_COMMIT_REF_SLUG"
    APP_DIR: "adm"

docker-build-login:
  extends: .docker-build  
  dependencies:
    - build-login
  needs:
    - build-login
  artifacts:
    paths:
      - dist/login
  variables:
    IMAGE: "registry.gitlab.com/plataformazw/zw_ui/login:$CI_COMMIT_REF_SLUG"
    APP_DIR: "login"

docker-build-crm:  
  extends: .docker-build
  dependencies:
    - build-crm
  needs:
    - build-crm
  artifacts:
    paths:
      - dist/crm
  variables:
    IMAGE: "registry.gitlab.com/plataformazw/zw_ui/crm:$CI_COMMIT_REF_SLUG"
    APP_DIR: "crm"

tag-version:
  image: keviocastro/node-build
  when: on_success
  dependencies:
    - docker-build-treino
    - docker-build-canal-cliente
    - docker-build-adm
    - docker-build-login
    - docker-build-crm
  except:
    variables:
      - $CI_COMMIT_MESSAGE =~ /Bump version:/    
  only:
    - master
  script:
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
    - npm version patch -m 'Bump version:%s'
    - git tag -f latest -m "Deploy tag"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH -f --tags
