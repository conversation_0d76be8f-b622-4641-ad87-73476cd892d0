{"extends": "tslint:recommended", "rules": {"forin": false, "new-parens": false, "jsdoc-format": false, "variable-name": false, "array-type": false, "no-string-literal": false, "arrow-parens": false, "deprecation": {"severity": "error"}, "component-class-suffix": true, "contextual-lifecycle": true, "directive-class-suffix": true, "directive-selector": [false, "attribute", "app", "camelCase"], "component-selector": [false, "element", "app", "kebab-case"], "import-blacklist": [true, "rxjs/Rx"], "indent": [true, "tabs", 4], "interface-name": false, "max-classes-per-file": false, "max-line-length": [true, 200], "member-access": false, "member-ordering": [true, {"order": ["static-field", "instance-field", "static-method", "instance-method"]}], "no-consecutive-blank-lines": false, "no-console": [true, "debug", "time", "timeEnd", "trace"], "no-empty": false, "no-inferrable-types": [false, "ignore-params"], "no-non-null-assertion": true, "no-redundant-jsdoc": true, "no-switch-case-fall-through": true, "no-var-requires": false, "object-literal-key-quotes": [true, "as-needed"], "object-literal-sort-keys": false, "ordered-imports": false, "no-duplicate-imports": true, "import-spacing": true, "quotemark": [false], "trailing-comma": false, "no-conflicting-lifecycle": true, "no-host-metadata-property": true, "no-input-rename": true, "no-inputs-metadata-property": true, "no-output-native": true, "no-output-on-prefix": true, "no-output-rename": true, "no-outputs-metadata-property": true, "semicolon": true, "template-banana-in-box": true, "template-no-negated-async": true, "use-lifecycle-interface": true, "use-pipe-transform-interface": true}, "rulesDirectory": ["codelyzer"]}