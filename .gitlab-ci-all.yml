stages:
  - build
  - build-docker
  - tag

build-all:
  image: node:12
  tags:
    - docker
    - large
  variables:
    ENABLE_LOGIN_CAPTCHA: 'false'
  only:
    - merge_requests
    - master
  stage: build
  artifacts:
    paths:
      - dist/
  script:
    - NODE_ENV=production yarn install --production 2> >(grep -v warning 1>&2)
    - yarn build-libs 2> >(grep -v warning 1>&2)
    # # Este compila somente para pt
    # - yarn build-apps 2> >(grep -v warning 1>&2)
    # Compila todas as linguagens
    - yarn build-all-apps 2> >(grep -v warning 1>&2)

.docker-build: &docker-build-template  
  variables:
    DOCKER_TLS_CERTDIR: ""
  dependencies:
    - build-all
  tags:
   - locaweb
   - swarm
  only:
    - merge_requests
    - master
  stage: build-docker
  artifacts:
    paths:
      - dist/
  script:
    - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
    - docker build -t $IMAGE --build-arg app_dir=$APP_DIR .
    - docker push $IMAGE

docker-build-treino:  
  extends: .docker-build
  variables:
    IMAGE: "registry.gitlab.com/plataformazw/zw_ui/novo-treino:$CI_COMMIT_REF_SLUG"
    APP_DIR: "treino"

docker-build-canal-cliente:  
  extends: .docker-build
  variables:
    IMAGE: "registry.gitlab.com/plataformazw/zw_ui/canal-cliente:$CI_COMMIT_REF_SLUG"
    APP_DIR: "canal-cliente"

docker-build-login:
  extends: .docker-build  
  variables:
    IMAGE: "registry.gitlab.com/plataformazw/zw_ui/login:$CI_COMMIT_REF_SLUG"
    APP_DIR: "login"

docker-build-crm:  
  extends: .docker-build
  variables:
    IMAGE: "registry.gitlab.com/plataformazw/zw_ui/crm:$CI_COMMIT_REF_SLUG"
    APP_DIR: "crm"

tag-version:
  image: keviocastro/node-build
  stage: tag
  when: on_success
  except:
    variables:
      - $CI_COMMIT_MESSAGE =~ /Bump version:/    
  only:
    - master
  script:
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
    - npm version patch -m 'Bump version:%s'
    - git tag -f latest -m "Deploy tag"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH HEAD:$CI_COMMIT_REF_NAME
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" <NAME_EMAIL>:$CI_PROJECT_PATH -f --tags
