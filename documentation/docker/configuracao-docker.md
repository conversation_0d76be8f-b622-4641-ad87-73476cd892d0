# Configuração Docker Pacto

- Acesse [Instalação docker windows](https://docs.docker.com/desktop/install/windows-install/) e siga o passo a
  passo para realizar a configuração do Docker no windows.
- Após a instalação do docker acesse [https://gitlab.com/-/profile/personal_access_tokens](https://gitlab.com/-/profile/personal_access_tokens)
  para criar o token de acesso a api do gitlab. Insira o nome do token, remova a data de expiração, selecione o scopo
  <b>api</b> e clique em <b>Create personal access token</b>. Conforme imagem abaixo:
  ![Configuração do token de acesso](config-acess-token.png)

- Após criar o token, copie e salve-o em um arquivo para que não perca pois não terá como recuperá-lo posteriormente.
  ![Copiar token de acesso](config-acess-token-gerado.png)

- Agora execute o comando abaixo, informe o login do gitlab e na senha o token gerado anteriormente:
  `docker login registry.gitlab.com`

## Configurando o docker compose

- Copie o arquivo [docker-compose.yml](/docker-compose.yml) para uma pasta externa de seu computador.
- onde está {IP_MAQUINA} altere para o ipv4 do seu computador
  - Para descobrir o ip no windows execute ipconfig no terminal
  - Para descobrir o ip no linux execute ifconfig no terminal
- Para criar o container de algum serviço em específico deve ser executado o comando abaixo (nome-service: Nome do
  serviço encontrado no docker-compose.yml)
  - `docker-compose up -d <nome-service>`
  - O serviço é encontrado dentro do docker-compose.yml, após a propriedade services, conforme imagem:
    ![Service docker](encontrar-service-docker.png)
- Os serviços que precisam ser executados são
  - docker compose up -d postgres dynamodb oamd zw treino login autenticacao discovery
- Os outros serviços podem ser executados conforme a necessidade

## Subir um serviço em uma branch específica

A imagem de um serviço é apontado para o registry do gitlab que segue a estrutura abaixo:
`registry.gitlab.com/{nome_grupo}/{nome_projeto}/{nome_imagem}:{nome_tag}`

Portanto para mudar a branch basta mudar o `nome_tag` para a tag específica e executar o comando
`docker-compose up -d --force-recreate nome_servico`.

Caso você não tenha acesso ao projeto que precisa o nome da tag segue o seguinte padrão:

Nome da branch: hotfix/AD-5434_exemplo_2
Nome da tag: hotfix-ad-5435-exemplo-2