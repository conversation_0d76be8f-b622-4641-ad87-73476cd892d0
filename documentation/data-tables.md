

# Data Tables

Esse documento contem pattern e os conceitos básicos a serem seguidos para se construir tabelas dinâmicas de forma escalável, facilitando a construção desses elementos e reutilizando código sempre que possível.


## Fluxo de dados

Um componente de tabela deve ser responsável apenas por renderizar o conteúdo da tabela e deve ser reponsável por gerir apenas o estado necessário para tal. Os seguintes elementos são de responsabilidade de uma data table:

1. Pagina atual
2. Tamanho da página
3. Coluna pela qual estamos ordenando
4. Direção de ordenação
5. Quais colunas estão visíveis
6. <PERSON>uais colunas são ordenáveis
7. a lista de items sendo renderizada
8. O estado da tabela
   1. "loading" - aguardando informações
   2. "error" - houve um erro ao obter as informações
   3. "normal" - renderizando informações da tabela 

É importante que seja abstraído do componente da tabela de onde os dados estão sendo obtidos. Por exemplo, esses dados podem ser obtidos de um endpoint, de vários ou podem ser existir apenas no front-end. Seja qual for a fonte dessa informação o componente responsável por renderizar a tabela não deve se preocupar com isso. O diagram a seguir ilustra o fluxo de eventos e informações durante uma interação com uma data table:

![Arquitetura do Monorepo](/documentation/data-tables-fluxo-dados.png)

No diagrama (i) o bloco verde representa o template do componente raiz, o qual está fazendo o uso do componente de relatório; (ii) o bloco laranja é o controller do componente raiz e será o responsável por obter os dados da API e repasa-los para o data table.

Ao interagir com o data table, alterando a página atual por exemplo, o fluxo de eventos que ocorre é defindo abaixo:

1. O usuário altera a página atual
2. Isso é capturado dentro do componente e é emitido através de um @Output. O objeto emitido contem as informações necessárias para se obter os dados da API (página atual, tamanho da página, ordenação e algum filtro adicional).
3. O controller raiz obtem essa informação e faz uso dela para obter os dados através de um adapter de api.
4. Ao obter a resposta, a mesma é ajustada se necessário e repassada para o data table através de um @Input diretamente, ou através de algum emissor de evento (BehaviorSubject, por exemplo).


## Tipos de componente/diretiva para construção de relatórios

Para simplificar a construção de tables dinâmicas podem definir a existência de 3 tipos de elementos (componentes/diretivas), definidos logo a seguir:

### Diretivas de estilo

O primeiro tipo tem o objetivo de aplicar estilos CSS em HTML de *table*. Ou seja, não aplica nenhum tipo de comportamento como listagem de items, paginação, refresh dos dados. Basicamente, nessa categoria nenhum elemento do DOM é inserido, removido ou editado. Ou seja, todo o *html* involvido nessa modalidade é definido de forma manual pelo dev. 

Melhor forma de se fazer isso é atraves de diretivas que aplicam estilos de tables específicas. Dessa forma, cada diretiva pode aplicar os estinos necessários diretamente no **element.style**, por exemplo:

```
<table pacto-table-treino>
    <thead>
        <tr>
            <th pacto-cell-header-treino> Nome </th>
            <th pacto-cell-header-treino> CPF </th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td pacto-cell-treino> Pessoa 1 </td>
            <td pacto-cell-treino> 123.345.423-56 </td>
        </tr>
    </tbody>
</table>
```

No exemplo acima, as diretivas apresentadas aplicão o padrão de tabelas usadas no módulo treino. Pode ser criada diretivas para cada módulo ou grupo de funcionalidades se necessário.

### Diretivas de comportamento

A próxima categoria tem o propósito de aplicar comportamentos em tabelas sem se preocupa com a mesma é estilizada. Essa categoria geralmente, faz modificações no DOM: inserindo, modificando e removendo nós. Um exemplo de como isso pode ser feito, é criando uma diretiva que aplica o comportamento de DataTable com os seguintes comportamentos:

1. Lista de forma dinâmica os item da tabela
2. Gere paginação
3. Gere ordenação
4. Permite alterar visibilidade de cada columa
5. Renderiza um template específico para quando a tabela estiver em estado "loading"
6. Permite customizar o template do elemento que controla ordenação (normalmente, esse elemento são icones de seta que indicam a direção da ordenação) 

A interface desse deretiva, se resume aos seguintes items:

1. Definir o template do header para cada coluna
2. Definir o template do corpo para cada coluna
3. Definir o template do **tr** do corpo da tabela
4. Passar para diretiva um BehaviorSubject que emite o estado da tabela toda vez que ocorrerem mudanças
5. Podemos também permitir a definição de outros elementos como (i) template do loading state e (ii) template do controle do sort.

Um exemplo de como tam componente seria usado é:

```
<table pctDataTable [stateManager]="tableState">

    <ng-container pctDataTableColumn="nome">
        <th *pctTableHeaderCell> Nome </th>
        <td class="nome" *pctTableCell="let item;"> 
            {{ item.nome }}
        </td>
    </ng-container>

    <tr *uiTableRow></tr>

</table>
```

**Ambas as diretivas mencionadas acima podem ser combinadas para se obter uma data table estilizada.**

### Componentes especializados customizados

O ultimo nível de especialização é um componente de geração de relatório. Esse nível de componente tem os seguintes objetivos:

1. Aplicar estilos específicos a uma categoria de tables.
2. Aplicar comportamentos de data table (internamente o componente faz o uso da diretiva dataTable).
3. Incluir elementos visuais recorrentes para aquela categoria
   1. Filtros de texto ou filtros com dropdown no header ou footer da table
   2. Controles de ordenaçao ou paginação com um estilo específico para aquela aplicação
   3. Qualquer elemento visual que seja relevante mas que não seja parte integral da table.
4. Apesar de definir elementos predefinidos esse nivel de componente pode também aceitar customizações das columas de forma pareceida a diretiva DataTable.
5. Essencialmente, mantemos customização das colunas e acesso direto ao HTML porem abstraindo os elementos recorrentes.

Um exemplo de assinatura um componente nesses padrões:

```
<pacto-pactopay-data-table 
    [data]="tableData"
    [loading]="tableLoading"
    (filterUpdate)="filterUpdate($event)"
>
    <ng-container uiTableColumn="nome">
        <th *pctTableHeaderCell> Nome </th>
        <td *pctTableCell="let item;"> {{ item.nome }} </td>
    </ng-container>

</pacto-pactopay-data-table>
```

## Construindo tabelas dinâmicas da plataforma

Uma opção é criar diretivas de estilo para os principais tipos de tabela. 

Já os comportamentos pode ser capturados em algumas direivas de comportamento: (i) DataTable, (ii) EditableDataTable, (iii) SelectableDataTable, e (iv) ColapsableDataTable. O código em comum entre elas pode ser compartilhado através de uma através de uma diretiva base.

Em seguida, para tabelas recorrentes podem ser criados componentes específicos que faze uso das diretivas de comportamento adequado. 

Com base nessa estrutura existem 4 forma de se utilizar esse elementos:

1. Fazendo o uso do componente completo
2. Fazendo o uso de uma diretiva de comportamento e uma de estilo
3. Fazendo o uso apenas de uma diretiva de estilo
4. Fazendo o uso apenas de uma diretiva de comportamento

Deve se analizar qual resultado está sendo obtido para definir qual a melhor opção.
