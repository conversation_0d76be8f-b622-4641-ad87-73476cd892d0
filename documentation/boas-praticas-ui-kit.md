

Esse documento contém um resumo das principais etapas do processo de criação de elementos para um catálogo de componentes. Alé<PERSON> disso, fala também de algumas formas de organizar o mesmo de modo a facilitar a manutenção e a utilização do mesmo. Por fim, é definio uma lista de boas práticas que pode ser aplicadas ao longo do processo para aumentar a produtividade e reduzir os recursos necessários para fazer a manutenção do catálogo.

# Ciclo da criação de um componente

O processo é criação de elementos para um catálogo pode ser modelada por um ciclo de 7 etapas.

## Etapa 1 - Design de tela
Nessa etapa o time de UI/UX propôe a tela se baseando nos requisitos definidos pelo time de produto. Durante esse processo, o time de design deve usar como referência o catálogo existente para fazer uso de componentes que já existam sempre que possível. É responsabiliade do UI/UX esclarecer junto ao time de produto qualquer duvida relacionada ao funcionamento dessa feature.

## Etapa 2 - Proposta de novos componentes
Caso componentes atuais não atenda todas as demandas do produto, mesmo se ajustes fossem feitos, time de UI/UX propõe ao DEV que novos componentes sejam criados. Importante avaliar que criação de um novo componente involve tempo e planejamento, portanto deve-se avaliar a real necessidade desse novo elemento.

## Etapa 3 - Avaliação de viabilidade
Apos receber proposta de novos compoentes do time de design, é responsabilidade do DEV fazer uma avaliação sobre a viabilidade do componente e propor ajustes se necessário. Os passos desse processo são:

1. Avaliação técnica: é possível implementar o visual/comportamento?
1. Dev negocia com time UI/UX sobre possibilidades de usar componentes parecidos. Pode ser viável fazer o uso temporário de componente mais simples em MVP.
1. Levar em consideração custo/benefício durante essa análise.
1. Sugerir ajustes que tornam elemento proposto mais reutilizável e/ou mais fácil de implementar.
1. Avaliar ser componente deve fazer parte do módulo ou do catálogo.
1. Dev propõe uma interface/assinatura para o componente
1. Chapter “front-end” revisa proposta, faz recomendações e aprova.

## Etapa 4 - Detalhar comportamento
Com a proposta do componente aprovada o proximo passo é definir em detalhe o comportamento.
Esse processo é de responsabilidade do time de UI/UX e é importante para que não fiquem dúvidas sobre como o componente deve funcionar:
* Comportamento quando ocorrer erros
* Quando dados estiverem carregando
* Quando não houverem resultados (empty state)
* Definir visual para cada um dos estados
* Definir comportamento para diferentes estados

## Etapa 5 - Implementação
Após todo o planejamento feito e concluído pode-se começar a implementação do componente de fato. Idealmene, deve ser a etapa mais fácil se os passos anteriores foram feitos adequadamente.
Lembrar de tirar duvidas de comportamento e requisito com time de UI/UX e de implementação com chapter front-end.

## Etapa 6 - Validação com UI/UX (UAT)
Depois que componentes estiver implementado deve ser apresentado ao time de UI/UX para validação. Se comporta como esperado? Tem o visual esperado? Existem pequenos ajustes que podem ser feitos? Problemas/casos que não foram previstos durante o planejamento podem surgir nessa etapa. Ao fim dessa etapa ambos times (UI/UX e DEV) devem estar satisfeitos com o resultado.

## Etapa 7 - Catalogar e Documentar
Agora que o componente está implementado e valido é imprescindível que seja documentado e inserido no catálogo do Storybook. Pontos importantes a se considerar:
1. Se novo DEV entrar no time, o mesmo conseguiria utilizar o componente fazendo o uso apenas da documentação?
1. Está claro para o time UI/UX quais os comportamento que o componente entrega na documentação textual?
1. A documentação deve conter o propósito do componente? Isso previne sobrecarga do componente ao longo do tempo. As diferente formas como pode ser usado, melhor forma de se fazer isso são com exemplos.

## Fim do ciclo
O ciclo se finaliza e agora é possível dar continuidade na criação da tela que deu início ao processo. Em features grandes esse ciclo se repete várias vezes!.

# Boas práticas

1. Exponha a interface HTML
   * A interface HTML se refere a ter acesso aos seguintes items: captura de eventos, aplicar css, html customizado
   * Componentes que encapsulam as tags de html não são extensíveis e acarreta em componentes complexos e com inúmeros inputs.
   * Algumas formas de se fazer isso: (i) preferência a diretivas sempre que possível, (ii) faça uso de transclusão e (iii) crie componentes que usam diretivas estruturais para customizações.
1. Evite sobrecarga de componente
   * Componentes que fazem demais são complexos, difícil de testar e fazer mudanças
   * Um componente complexo pode ser dividido em componentes especializados mais simples. Código em comum entre eles pode ser compartilhado para evitar duplicação.
1. Componentes simples e semânticos
   * Componentes devem ser simples de entender, usar e testar
   * Definir componente e inputs de forma semântica, deve ser evidente pelo nome dos inputs/seletores/diretivas o que fazem.
   * Faça o uso de tipagem TypeScript
1. Aplique o conceito de separação de conceitos
    * Componentes de interface fazem parte de uma camada de apresentação
    * Recebem informações por input, e html (na grande maioria dos casos)
    * Não se comunicam com uma API para obter dados
    * Não guardam estado sobre a rota sendo visualizada ou aplicação como um todo. Apenas os valores necessários para renderizar elementos no DOM.
    * Não devem acessar contextos da aplicação por DI (injeção de dependência)
1. Desacoplamento de CSS
    * Na depender de estilos definidos em camadas superiores que estejam fora do componente.
    * Todo CSS necessário para construir o comportamento visual de um componente deve estar dentro do mesmo.
    * Define CSS em um componente de forma que permita extensibilidade de estilos.
1. Qualidade em vez de Quantidade
    * Caso não seja possível investir o tempo adequado, evitar criar componente.
    * Um catálogo saudável é conciso e não possui elementos duplicados
1. Evite bibliotecas de terceiros.
    * Tem vários pontos negativos:
      * Fica acoplado ao código de terceiros
      * Menos liberdade e flexibilidade
      * Essas bibliotecas podem ser descontinuadas ao longo do tempo
      * Aumento da complexidade para configurar e testar







