
# Fragmentando a plataforma

A medida que a plataforma se torna cada vez maior é cada vez maior a necessidade de sub-dividir a plataforma em aplicações menores. Isso permite que que os deploys sejam feitos de forma independente e facilitam a resolução de bugs pois reduz a complexidade da cada uma das aplicações. Um outro benefício de fazer isso é permitir que vários times trabalhem de forma independente nessas aplicações. Alguns dos problemas gerados ao fazer essa fragmentaçao:

* Como navegar entre as aplicações
* Links para certas rotas podem fazer ser internos ou apontar para outras aplicações
* Como manter sessão do usuário, Não queremos logar novamentes ao trocar entre as aplicações
* Como reutilizar código usados para gestão de estado de sessão e inicialização entre outros…

Existem também alguns problemas gerados específicamente devido o menu global. Dado que temos várias aplicações com vários módulos (treino, cross, adm, …). Deve ser possível, na busca do menu global, fazer encontrar uma funcionalidade de outra aplicação. Como fazer isso sem duplicar as funcionalidades (rotas, rótulos e configurações)?

## Navegação

### Organização interna da aplicação

Para facilizar o entendimento e organização da plataforma faz-se necessário definir os diferentes elementos que compõem a plataforma e definir sua hierarquia. A imagem a baixo mostrar esses diferentes elementos:

![Arquitetura do Monorepo](/documentation/niveis-fragmentos-plataforma.jpg)

* Aplicações - São o nível mais alto. São deployados de forma independente e podem conter vários módulos.
* Módulo - são uma divisão de negócio
  * São os módulos vistos pelo cliente final (treino, crossfit, agenda, adm). 
  * Tem diferenças visuais, cada módulo possui uma cor, etc...
  * Não se refere ao conceito de módulo do Angular
* Rotas - cada rota da aplicação para qual existe uma implementação e um controlador associado
  * Um módulo é composto por inúmeras rotas
  * Exemplo: /treino/fichas, /treino/fichas/12

### Tipos de navegação

Tendo em mente essas níveis de organização existem dois tipos de navegação entre rotas no que diz respeito a aplicações: (i) navegação interna, ocorre dentro da mesma aplicação e (ii) navegação externa, ocorre entre aplicaões diferentes. Uma outra forma de organiar navegação é rem relação a estrutura do link (i) estático, não possui parâmetros na url e (ii) dinâmico, possui parâmetros.

É necessários centralizar a gestão da navegação inter-app para ambos links estáticos e dinâmicos. Isso irá permitir que seja feita a navegação entre aplicações de forma automatizada.

Para fazer isso podemos modelar essas entidades como indicado logo abaixo. Todas esses artefatos podem fazer parte da biblioteca SDK.

```

export enum PLATAFORM_MODULE_ID {
    TREINO = 'TREINO',
    ...
}

export enum PLATAFORM_APPLICATION_ID {
    TREINO_APP = 'TREINO_APP',
    ...
}

export interface PlataformApplication {
    id: PLATAFORM_APPLICATION_ID;
    deployUrl: string;
}

export interface PlataformModule {
    id: PLATAFORM_MODULE_ID;
    applicationId: PLATAFORM_APPLICATION_ID;
    url: string;
    label: string;
    initials: string;
    themeColor: string;
}

export interface PlataformRoute {
    label?: string;
    isDynamic?: boolean,
    url: (...args: number[]) => string
}

export interface ModuleRoutes { [key: string]: PlataformRoute };

export interface PlataformRoutes { [moduleId: string]: ModuleRoutes };

```

Detalhes de como isso é feito podem ser encontrados no repositório de [exemplo](https://github.com/harleivicente/subdiving-angular-plataform).


## Estado

Um dos problemas que devem ser resolvidos ao segmentar a aplicação e a gestão do estado de sessão para que não seja necessário refazer o login ao trocar entre aplicações. Caso seja possível disponibilizar as diferentes aplicações no mesmo domínio/sub-domínio temos a opção de compartilhar o estado através do local storage. Uma solução simples e fácil de implementar. Para lidar com possíveis problemas de segurança podemos garantir que o token gerado para um cliente em um certo IP apenas funcione para requisições feitas daquele mesmo IP. Essa estratégia protege contra ataques onde o token é obtido tendo acesso a maquina onde algum usuário tenha feito o login. A imagem abaixo exemplifica essa proposta.

![Compartilhando estado](./local-storage-state.jpg)

### Compartilhando lógica de gestão

Idealmente não devemos duplicar lógica que gere estado entre as apps da plataforma. Podemos compartilhar esse “objetos” através de uma lib (SDK). Exemplos de elementos a serem compartilhados incluem: interfaces, types e a implementação de certos comportamentos básicos (login e logout), Definição de classes abstratas que podem ser implementadas/estendidas pelas apps, Interface de comunicação com o LocalStorage.

Todos esses artefatos podem ser abstraídos e compartilhado através do SDK, como indicado no diagrama abaixo:

![Gestão de estado](./artefatos-gestao-estado.jpg)

Um exemplo detalhado de como isso pode ser implementado pode ser encontrado no repositório de [exemplo](https://github.com/harleivicente/subdiving-angular-plataform).