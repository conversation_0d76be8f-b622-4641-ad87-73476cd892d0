# Arquitetura do Sistema
O repositório ZW_UI é configurado como um workspace multi-projeto, esse processo é explicado em detalhe [aqui](https://angular.io/guide/file-structure#multiple-projects). Essencialmente, segmentamos a aplicação em pedaços menores para incentivar divisão de código, desacoplamento e simplicar os processo de build, teste e deploy da aplicação. Existem dois tipos principais de sub-projetos, defindos logo abaixo.

## Aplicações

Um aplicação é um projeto que é construído pra ser visualizado no browser. Possui componentes, serviços e, mais importante, faz uso do Router do angular para acoplar rotas do browser a componentes. A principal característica de applicações são que podem rodar as mesmas e visualizá-las no browser.

Para se criar uma aplicação (com o terminal dentro da pasta raiz):
```
   > ng generate application application-name
```

Para rodar uma application e visualiza-la no browser:
```
   > ng serve application-name
```

## Bibliotecas

Já uma biblioteca, ou lib, é uma coleção de componentes, serviços e utilitários que podem ser usados em aplicações. Uma lib não pode ser 'rodada' nem visualizada diretamente no browser. O propósito de uma biblioteca é conter elementos comuns entre outras aplicações.  

Para diferenciar entre os projetos que são libs e os que são aplicações basta inspecionar o arquivo angular.json (localizado na raiz do projeto). Para cada projeto dentro do workspace existe um item no atributo **projects**. Dentro desse item existe um atribute chamado "projectType", o qual pode ser "application" ou "library".

## Estrutura Atual 

Atualmente a plataforma possui as aplicações:

| Aplicação        |  Pasta                      | Time Responsável | Descrição Produtos                                             |
|    :---          |  :---                       | :--- |       ---:                                                     |
| plataforma-pacto |  src                        | Kaizen, Payments | Treino, Pessoas, Crossfit, Avaliação Fisica, Agenda, Graduação, PactoPay|
| adm              |  projects/adm               | AD | Adm.                                                           |
| canal-cliente    |  projects/canal-cliente     | - | Canal do Cliente.                                              |

e as seguintes bibliotecas:

| Aplicação        |  Pasta                      | Time Responsável | Descrição Produtos                                             |
|    :---          |  :---                       | :--- |       ---:                                                     |
| ui               |  projects/ui                | Todos | Biblitoeca de elementos atual da plataforma.                   |
| skd              |  projects/sdk               | Todos | Possúi lógica comum como gestão de permissão e sessão.         |
| old-kit-ui       |  projects/old-ui-kit        | Todos | Biblioteca visual antiga de componentes. **Depracated!!**      |

_Vários dos padrões definidos na seção a seguir nao estão atualmente sendo seguidas. Portanto, algumas partes da plataforma precisarão ser refatoradas_

## Padrões da arquitetura

O diagrama abaixo ilustra como os módulos devem ser organizados:

![Arquitetura do Monorepo](/documentation/monorepo-arquitetura.png)

**Padrão 1** - Cada produto deve se tornar uma applicação e sempre que possível deve ser subdivida em várias aplicações menores.
* Como são indepentendes essas aplicações serão desenvolvidas de forma isolada.
```
   > ng serve treino
   > ng serve pactopay
   > ...
```
* Da mesma forma serão buildadas de forma isolada:
```
   > ng build treino
   > ng build pactopay
   > ...
```
* E como são independentes, serão acessadas de forma individual. Por enquanto mate-las deployadas no mesmo dominio de modo que podemos fazer o uso do localStorage para compartilhar sessão. Em um próximo passo podemos usar de outras estratégias (micro-front end por exemplo).
```
   Treino -> www.pactosolucoes.com.br/treino
   PactoPay -> www.pactosolucoes.com.br/pactopay
   ...
```
**Padrão 2** - Todos os componentes visuais que sejam comums e recorrementes em mais de um módulo deve ser migrado para o UI Kit.
* Componentes que só tem utilizade apenas em um das aplicações não deve ir para o UI Kit.
* Ao serem migrados para a biblitoca qualquer referencia a regras de negócios devem ser removidas do componente de modo que o mesmo seja abstrato.
* Componentes não utilizados em vários lugares devem ser removidos.
* Fazer o uso de teste unitário.

**Padrão 3** - Utilitários comuns, como gestão de sessão de login, permissões de acesso e outros devem ser colocados em biblotecas compartilhadas.
* Remover referencias e menções a regras de negócio antes de migrar.
* Fazer o uso de teste unitários.

**Padrão 4** - Aplicações pode depender apenas de bibliotecas e nunca de outras aplicações.
* Exemplo: A aplicação do Treino não deve fazer import de componentes definidos em outra aplicação. 

**Padrão 5** - Bibliotecas pode depender apenas de outras bibliotecas.
* Exemplo: Uma biblioteca não deve referenciar código definido dentro do Treino. 