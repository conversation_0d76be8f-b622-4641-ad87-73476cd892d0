
# Code Quality Checklist

_Segue uma lista dos principais conceitos e padrões a serem seguidos no projeto divididos por categoria. Esse documento deve evoluir junto com o projeto e com o estilo e decisões da equipe que mantém o projeto. Esse documento deve ser usado como referência durante code review._

## Arquitetura do Monorepo

1. Componentes de interface que sejam pertinentes a várias aplicações devem ficar no UI-Kit.
1. Código referente a comunicação com a API de ficar em um adaptador de API.
2. Utilitários e ferramentas de gestão de estado, controle e navegação entre aplicações devem fazer parte do SDK.
3. Utilitários que permitem a navegação entre aplicações deve ser mantido no SDK.
4. Cada produto deve se tornar uma aplicação e sempre que possível deve ser subdividida em várias aplicações menores.
5. Aplicações podem depender apenas de bibliotecas (UI-Kit e SDK) e nunca de outras aplicações.
6. UI-Kit não deve depender de nenhuma aplicação, porém pode depender do SDK.
7. SDK não deve depender de aplicações ou UI-Kit.
8. Navegação intra-aplicação, ou seja, entre rotas da mesma aplicação, devem ser feitas usando o router do Angular. Dessa forma a página não é recarregada.
9. Navegação inter-aplicação, ou seja, entre rotas que residem em aplicações independentes, deve ser feita através de um redirect. 

## Adapters de API

1. Os adaptadores devem seguir os padrões de arquitetura definidos. [(padrões para adaptadores de API)](./adaptadores-api.md).
2. Lógica de comunicação com APIs internas deve ser extraída para uma biblioteca dentro do mesmo repositório.
3. Um adapter não deve depender de outro adapter.
4. Um adapter pode ser dependente de uma biblioteca utilitária. Por exemplo, interfaces comuns a vários adapters podem residir na biblioteca SDK.
5. Uma aplicação pode depender de vários adapters diferentes.
6. Uma API adapter deve permitir comunicação apenas com uma API.

## Catálogo de componentes

1. Componentes devem expor a interface HTML. **Explicação:** Componentes que encapsulam as tags de html não são extensíveis e acarretam em componentes complexos e com inúmeros inputs. Algumas formas de se fazer isso: (i) preferência a diretivas sempre que possível, (ii) faça uso de transclusão e (iii) crie componentes que usam diretivas estruturais para customizações.
1. Componentes não devem ser sobrecarregados. **Explicação:** Um componente complexo pode ser dividido em componentes especializados mais simples. Código em comum entre eles pode ser compartilhado para evitar duplicação.
1. Componentes devem ser simples e semânticos. **Explicação:** Definir componentes e inputs de forma semântica, deve ser evidente pelo nome dos inputs/seletores/diretivas o que fazem.
1. Componentes de apresentação não devem se comunicar com uma API.
1. Componentes de apresentação só guardam estado pertinente a renderização seus elementos no DOM.
1. Componentes de apresentação não devem acessar contextos da aplicação por DI.
1. Componentes devem ser devidamente tipados com Typescript.
1. Componentes não devem depender de estilos definidos em camadas superiores ou globalmente.
1. Evitar uso de bibliotecas de terceiros a não ser que seja explicitamente definido como necessário pelo SQUAD de frontend.
1. **Componentes de relatório** devem ser construídos usando os padrões definidos no projeto. [(padrões para construção de relatórios)](./data-tables.md).
1. **Componentes de formulário** devem ser construídos usando os padrões definidos no projeto. [(padrões para construção de formulários)]().

## Organização dos controladores

1. Seguir padrões de arquitetura para controladores definidos no projeto. [(padrões para controladores)](./controladores-e-rotas.md).
1. A lógica e estado de um controlador não pode afetar outras. Ou seja, um controlador não deve depender do estado definido por outro.
1. Todas as informações necessárias para carregar os dados necessários na página devem estar na URL.
2. Fazer o unsubscribe para cada subscribe feito em um controlador para evitar vazamento de memória. Opcionalmente, fazer uso do pipe async como exemplificado [aqui](./controladores-e-rotas.md#fazendo-o-uso-so-async-pipe).


## Testes Unitários

1. Componentes, diretivas, serviços e pipes em módulos utilizados por várisa aplicações, como SDK e UI-KIT, devem ter cobertura de testes unitários.