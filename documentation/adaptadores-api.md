# Adaptadores de API

A plataforma se communica com uma série de API's diferentes, algumas delas internas (API do treino, API do PactoPay, etc) e outras de terceiros. Existem alguns conceitos que podemos seguir, em especial durante a comuniação com API's internals, para que a plataforma se torne mais padronizada e previsível.

Para se comunicar com uma API específica, no contexto do Angular, geralmente criamos alguns serviços, modelos e configurações alguns headers para serem enviados para a API. Podemos considerar toda essa lógica como um adaptador que nos permite comunicar com aquela API.


## Adaptadores usados na plataforma

| Adaptador             |  Pasta                        | Descrição          | Time Responsável |
|    :---               |  :---                         |       :---         | :---             |
| acesso-sistema-api    |  projects/acesso-sistema-api  | -                  | AD               |
| adm-core-api          |  projects/adm-core-api        | -                  | AD               |
| cadastro-aux-api          |  projects/cadastro-aux-api        | -          | AD               |
| clube-vantagens-api          |  projects/clube-vantagens-api        | -    | AD               |
| plano-api          |  projects/plano-api        | -                        | AD               |
| produto-api          |  projects/produto-api        | -                    | AD               |
| relatorio-api          |  projects/relatorio-api        | -                | AD               |
| treino-api          |  projects/treino-api        | -                      | Kaizen           |
| zw-pactopay-api          |  projects/zw-pactopay-api        | -            | Payments         |


## Organização dos Adapters

**Padrão 1** Toda a logíca de comunicação com cada uma das APIs internas deve ser extraída para uma biblioteca dentro do mesmo workspace, ou seja, dentro do mesmo monorepo. Isso traz alguns beneficios:

1. Reduzir tamanho, complexidade e responsabilidade das *aplicações*
2. Nos permite reutilizar toda a lógica desse adapter em outras aplicações
3. Facilida identificação de erros de forma mais pontual. Por exemplo, conseguimos verificar de forma indepentene se um erro está na aplicação ou em um adapter (com testes unitário).

*Caso a comunicação com uma API seja através de poucos endpoints e seja usada apenas em uma aplicação deve-se avaliar o custo-beneficio de extrair esses adapters.*

**Padrão 2** Um biblioteca de adapter não deve ser depentende de outro adapter. For exemplo, não se pode fazer o import de uma interface ou serviços que estão em outro adapter. Isso pode gerar dependencias ciclicas além de criar uma acoplamento indesejado.

**Padrão 3** Um adapter pode ser dependente de uma biblioteca utilitária. Por exemplo, interfaces comum a vários adapters pode residir na biblioteca SDK.

**Padrão 4** Um aplicação pode depender de vários adapters diferentes. Por exemplo, a aplicação Treino pode precisar se comunicar com a API do treino bem como a API do Adm. Para isso poderia importar os serviços desejados de ambos essas bibliotecas.

O diagrama abaixo ilustra os conceitos discutidos até o momento:

![Adaptadores de API](/documentation/adaptadores-api.png)


## Arquitetura de um Adapter

### Serviços

Um adapter deve ser composto por uma série de serviços que são resposáveis por alguns endpoints relacionados. Para exemplificar imagine uma API que gerencia uma escola, a qual chamaremos EscolaApi. O adapter para essa API poderia ter um serviço responsável por gerenciar os alunos (EscolaApiAlunoService) e um responsável por gerenciar os professores (EscolaApiProfessoresService). Cada um desses serviços poderia ter os seguintes métodos:

1. **EscolaApiAlunoService**
```
    public criarAluno(aluno: aluno: AlunoCreate): Promise<Aluno> { ... }
    public editarAluno(alunoId, aluno: AlunoEdit): Promise<Aluno> { ... }
    public removerAluno(alunoId): Promise<Boolean> { ... }
    public listarAluno(filtro): Promise<Aluno[]> { ... }
```
1. **EscolaApiProfessoresService**
```
    public criarProfessor(professor: ProfessorCreate): Promise<Professor> { ... }
    public editarProfessor(professorId, professor: ProfessorEdit): Promise<Professor> { ... }
    public removerProfessor(professorId): Promise<Boolean> { ... }
    public listarProfessor(filtro): Promise<Professor[]> { ... }
```
### Modelos

Todos as interfaces consumidas (argumentos) e retornadas pelos serviços de um adapter são de responsabilidade do mesmo. Isso quer dizer que devem estar definidos unicamente de do módulo que faz o uso do mesmo. A principal motivação por traz disso é manter código relacionado o mais próximo possivel.

### Configurando a request
No geral para se comunicar com uma API é necessário passar algum token de segurança, bem como adicionar alguns cabeçalhos que são específicos daquela api. Em vez de fazer isso em cada um dos métodos dos nossos serviços podemos criar um serviço abstrato configura de forma automatíca os headers, parametros e outros aspectos, no nosso exemplo ele se chamaria EscolaApiBase. O código que implementa o get, por exemplo, seria algo similar ao seguinte:
```

    @Injectable({
        providedIn: EscolaApiModule
    })
    export class EscolaApiBaseService {

    ...

    public get(url: string, options: RequestOptions = {}): Observable<T> {
        const apiConfig = this.getApiConfig();

        const fullUrl = `${apiConfig.baseUrl}/${url}`;
        const mergedOptions = this.mergeOptions(options, apiConfig);

        return this.httpClient.get(fullUrl, mergedOptions);
    }

    ...
```

Em seguida poderiamos fazer o uso desse serviço em todos serviços do nosso adpater e não seria necessário se preocupar em configurar as requests:

```
@Injectable({
    providedIn: EscolaApiModule
})

export class EscolaApiAlunoService {

    constructor(
        private baseService: EscolaApiBaseService
    ) { }

    obterAluno(id: number): Observable<Aluno> {
       const url = `aluno/${id}`;
       return this.get<Aluno>(url);
    }
```

Observe que podemos passar a responsabilidade de construir a url completa para essa clase abstrata EscolaApiBaseService, desta forma os serviços precisam se preocupar apenas em saber a url relativa do endpoint que implementam.


### Passando parametro de configurações para o adapter

Como queremos que esse adapter seja utilizado por várias aplicações diferentes precisamos que seja generico. Portanto, não podemos acoplar configurações como token JWT, endereço da API e outros. Dessa forma, fica na responsabilidade das aplicações que forem utilizar o adapter passar essas informações. Isso pode ser feito de forma fácil através do uso de [Dependency Injection](https://angular.io/guide/dependency-injection). Primeiramente definimos as configurações que nossa api precisa para funcionar:

```
export interface EscolaApiConfig {
    baseUrl: string;
    authToken: string;
}
```

Em seguida criamos uma Serviço abstrato que tem um método que retorna essa configuração de forma asyncrona. Isso permite que a aplicação que for usar o adapter possa fazer outras requisiões para obter essa informação, caso necessário.

```
@Injectable()
export abstract class EscolaApiConfigProviderBase {

    abstract getApiConfig(): Observable<EscolaApiConfig>;

}
```

Por fim, fazemos o uso desse serviço abstrato na nossa clase base EscolaApiBaseService. Observe que o decorador @Optional indica que essa informação não está presente, nesse caso devemos disparar um erro. Fica na responsabilidade de quem for usar o adapter de definir um serviço que implemente essas classe:

```
@Injectable({
    providedIn: EscolaApiModule
})
export class EscolaApiBaseService {

    private constructor(
        private httpClient: HttpClient,
        @Optional() private apiConfigProvider: EscolaApiConfigProviderBase
    ) {
        if (!this.apiConfigProvider) {
            throw Error("Não foi fornecida uma implementação para EscolaApiBaseService");
        }
    }

    public get(...): Observable<Object> {
        return this.apiConfigProvider.getApiConfig()
        .pipe(
            mergeMap(apiConfig => {
                // apiConfig será um objeto EscolaApiConfig
                // Podemos usar esses dados para configurar a request
            })
        );
    }
```

Observação: no exemplo acima foi usada o operador **pipe** em conjunto com o metodo **mergeMap**. Isso basicamente permite que o metodo **getApiConfig** seja concluído sendo que é asincrono. Para mais informações sobre [mergeMap](https://rxjs.dev/api/operators/mergeMap) e outros operadores [Rxjs](https://rxjs.dev/guide/overview).

O diagrama abaixo mostra os principais elementos de um adapter:

![Arquitetura de um adapter](/documentation/arquitetura-adapter.png)

## Usando um Adapter em uma aplicação

Após criado um adapter como defindo acima o próximo passe e importar ele nas aplicações onde é necessário. Para exemplificar vamos detalhar como seria configurado o módulo de exemplo EscolaApi na aplicação Adm.

### Importando os módulos

O primeiro passo é importar o módulo principal do adapter no módulo raiz da aplicação. No caso do adm, o módulo principal é o AppModule (projects\adm\src\app\app.module.ts). Isso garante que todos os serviços que criamos dentro do EscolaApiModule estarão disponíveis dentro dos componentes.

```
import { EscolaApiModule } from 'escola-api';

...

@NgModule({
    ...
    imports: [
        EscolaApiModule
    ]
    ...
})


```

### Configurando o adapter

O próximo passo configurar o adapter para funcionar com o Adm. Esse configuração é pertinente apenas ao adapter dentro do Adm, caso o adapter seja usado em várias aplicações diferentes devera ser configurado várias vezes.
Para fazer essa configuração basta implementarmos um serviço que implementa a interface *EscolaApiConfigProviderBase*. Em seguida devemos configurar o adapter de modo que use nossa implementação, podemos fazer isso usando um provider.

```

import { EscolaApiConfigProviderService } from 'escola-api';
import { EscolaApiConfigProviderService } from './config-adapters/escola-api-config-provider.service';

...

@NgModule({
    ...
    providers: [
        { provide: EscolaApiConfigProviderService, useClass: EscolaApiConfigProviderService }
    ]
    ...
})

```

O serviço *EscolaApiConfigProviderService* é um serviço que deve ficar na aplicação que está fazendo o uso do adapter. Nesse serviço basta implementar o método *getApiConfig*, como indicado abaixo:

```
import { EscolaApiConfigProviderBase } from 'escola-api';

@Injectable()
export class EscolaApiConfigProviderService extends EscolaApiConfigProviderBase {

    ...

    getApiConfig(): Observable<AcessoSistemaApiConfig> {

        const baseUrl = this.discoveryService.getUrlMap().acessoSistemaMsUrl;
        const authToken = localStorage.getItem('apiToken');

        return of({
            authToken,
            baseUrl,
        });
    }

}
```

**Esses são os passos necessários para se configurar um adapter.**

### Usando um serviço

Para se fazer o uso dos métodos disponíveis dentro de um adapter basta usar injeção de dependencia no componente. Por exemplo:


```
    import { EscolaApiAlunoService, Aluno } from 'escola-api';


    @Component({
        ...
    })
    export class AlunoList implements OnInit  {

        private aluno: Aluno;

        constructor(
            private escolaApiAlunoService: EscolaApiAlunoService
        )

        ngOnInit() {
            this.escolaApiAlunoService.obterAluno(id).subscribe((aluno) => {
                this.aluno = aluno;
            });
        }
        ...
    }


```
