
# Formulários

Relatórios são uma parte integral da aplicação pacto, portanto é importante que seja bem estruturada. Alé<PERSON> disso, devido o grande volume de formulários que precisam ser construídos é importante que seja construído uma série de componenetes reutilizáveis para se manter a padronização e acelerar a produção e entrega de novas features. Reaproveitar tais componentes também trás outros beneficios, facilita a manutenção e reduz o numero de bugs que surgem derivados de tais componenetes.

## Crie simples e que exponham o HTML
Componentes responsáveis por renderizar elementos de formulário devem ser simples na sua interface e implementação. Não devem fazer uso de contextos que contem estado associado a regras de negócios (exemplo: injeção de serviços) ou fazer comunicação direta com API's (em caso de selects que filtram no back-end).

Ao definir as interfaces dos componentes/diretivas exponha a interface HTML o máximo possível para maximizar a flexibilidade e capacidade dos componentes. Isso não é apenas mais fácil de fazer, mas também permite que o programador tenha mais liberdade. Entretanto, um efeito colateral dessa estratégia é que construir telas com esse estilo de componente se torna mais verboso. Portanto, 
é necessário achar um ponto de equilibrio que traga um bom custo benefício.

## Priorize componenetes nativos

Componentes nativos (checkbox, radio, text, textarea, select, …) possuem comportamentos definidos e implementados por padrão pelo browser. Alguns desse items inclue:
1. Alto níveis de acessibilidade
1. Indicação visual de foco (outline)
1. Navegação básica por teclado
    1. Muito importante em sistemas com muito formulários
    1. Enter (abrir select, ativar rádio, concluir formulário, etc…)
    1. Barra de espaço (ativar select, etc…)
    1. Setas teclado (trocar opção de select, abrir select)
1. Acessibilidade avançada
    1. Exemplo: leitores de tela para usuário com visão reduzida
    1. Essencial em certos sistemas, já em outros não 
    1. Para sistema da pacto não é tão relevante esse nível

## Quando construir componentes customizados?

Antes de tomar a decisão de construir um componente de formulário do zero verifique as seguintes afirmações:

* Não é possível atingir o resultado com ajustes em elementos nativos.
* Não é possível resolver o problema com uma combinação de elementos nativos.
* O time de UI/UX acredita que justifica o investimento de tempo e recurso.
* A equipe dispõe do tempo necessário para construir tal elemento com qualidade.

Caso qualquer uma dessas afirmações seja negativa, não é recomendado construir um componente do zero. Em vez disso, ajuste o design para que não precise de um elemento nativo.


## Customizando elementos nativos

Uma forma simples de estilizar elementos nativos é atravez do uso de diretivas que aplicam estilos de CSS direto no elemento em questão.

Entre as principais vantagens:
* Permite customização opcional.
* Permite customização que pode ser estendida.
* Permite customização com variações.
* Caso seja necessário existir dois tamanhos de select or exemplo.

Exemplos de diretivas criadas para estilizar inputs de texto e select nativos.

```
<form [formGroup]="formGroup" (submit)="submitHandler($event)">

    <!-- Input text nativo -->
    <label for="nome"> Nome </label>
    <input pctTextInput id="nome" formControlName="nome">

    <!-- Select nativo -->
    <label for="estado"> Estado </label>
    <select pctSelect id="estado" formControlName="estado">
        <option value> Selecione um Estado </option>
        <option value="GO"> Goias </option>
        <option value="SP"> São Paulo </option>
        <option value="MG"> Minas Gerais </option>
    </select>
    ...
</form>

```

## Gestão de field

Os principais items de um field de formulário são (i) label associado ao um input por id, (ii) mensagens de erro e (iii) tag de formulário (input, select). Gestão manual desse items pode ser simplificado com a criação de alguns componentes para gestão de fields. Os papéis desses componentes são:

* Associar o label ao input por id usando o atributo for
* Gerir visibilidade de mensagens de erro nas condição desejadas
* Estilização de label
* Estilização dos elementos de form em casos de erro.

Exemplo de interface sugerida:

```
<pct-form-field>
    <pct-field-label> Nome </pct-field-label>
    <input pctTextInput formControlName="nome">
    <pct-field-info> 
        Preencha seu nome completo para sabermos mais sobre você. 
    </pct-field-info>
    <pct-field-error *ngIf="nomeFc.hasError('required')"> 
        O nome é obrigatório. 
    </pct-field-error>
    <pct-field-error *ngIf="nomeFc.hasError('maxlength')"> 
        O nome não pode ser maior do que 20 caracteres.
    </pct-field-error>
</pct-form-field>
```

## Construindo elementos customizados

Em certas situações e customização de elementos nativos não é suficiente e não é possível fazer ajustes na interface. Em tais casos lembrar de levar em consideração os seguintes items como requisitos básicos para qualquer elemento de formulário customizado.

* Visibilidade de foco (feedback necessário para navegação com teclado).
* Navegação de teclado básica:
  * Enter
  * Key down, Key up
  * Tab
  * Envio de formulário com Enter
* Compatibilidade com gestão de forms nativo do Angular
  * Usando FormGroup, FormControl, FormArray, etc…
* Compatibilidade com fields automatizados, padrão discutido no segmento acima.