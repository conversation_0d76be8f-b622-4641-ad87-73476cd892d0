
Essa secção contém padrões de como se organizar os controladores e rotas de uma aplicação. Fala também de como organizar e gerenciar o estado mantido nesses controladores. Por fim, menciona algumas boas práticas a serem seguidas para se evitar algumas das causas de vazamentos de memória.

# Desacoplamento de funcionalidades

* A lógica e estado de uma view não pode afetar outras. Ou seja, uma view não deve depender de estado defino por outra view.
* Deve ser possível acessar a rota de forma direta.
* Todas as informações necessárias para carregar os dados devem estar na URL.

## Contra-exemplo

Para exemplificar um scenário de como não fazer imagine duas rotas (i) fichas e (ii) fichas/3/editar. A primeira lista todas as fichas já a segunda permite editar as informações específicas de uma ficha.

A seguinte configuração deve ser evitada pois cria acoplamento:
1. A view “fichas” obtém a lista de fichas da API.
2. Em seguida persiste essa informação em um serviço usado para manter estado.
3. Quando usuário acessar rota para editar, view obtém informações no serviço
4. Se usuário acessar “edit” diretamente causará um erro
5. Seria possível verificar se cache existe, e obter informação da API caso necessário no controlador de edição, porem, adicionaria ainda mais complexidade.


## Considerações parar funcionalidades com sub-rotas

Consideramos funcionalidades com sub-rotas aquelas que possúem rotas filhas. Geralmente, nesses casos, a rota pai é utilizada para renderizar algum parte do layout que seja comum a várias rotas diferentes. Por exemplo, imagine uma view onde o topo é fixo e o conteúdo do corpo pode mudar:
* Tela que mostra detalhes sobre uma turma
* Topo é fixo, com detalhes sobre uma turma
* Parte inferior do conteúdo pode ser (i) lista de alunos da turma ou (ii) formulário de criação de um novo aluno

Lembrar-se que ao trocar entre as diferentes rotas o estado do controlador pai é mantido pois se permanece naquele fragmento de rota. Porém, o estado dos controladores filhos são perdidos.

Conceitos que recomenda-se seguir em tais configurações:
* A lógica dos blocos filhos não pode depender um do outro. 
Exemplo: não supor que dados carregados na listagem de alunos pode ser usado na view de um aluno.
* Todas as 3 rotas devem funcionar independente de qual rota é acessada primeiro.
* Não usar o pai para compartilhar estado entre os filhos (não usar esse componente como ponte).
* Usar controlador PAI para guardar o estado que é utilizado no mesmo.
Caso os filhos precisem do estado do PAI pode compartilhar. Fluxo de informações sempre de cima para baixo.

## Vantagens do desacoplamento
* Reduz carga cognitiva (quando mais fácil de entender mais fácil de manter).
* Simplifica testes unitários (código fácil de testar => código bom).
* Ao fazer alterações em um único componente -> evita risco de quebrar duas features que poderiam ser independentes.

O benefício de se acoplar as rotas nesse caso é reduzir chamadas a API e melhorar experiência do usuário. Entretanto, existem outras formas de se atingir esse objetivo sem comprometer qualidade e clareza do código.


# Cache de dados da API

## Cache de API na camada de adapter

Uma das forma de se manter cache é gerenciá-lo na cada de adater. O cache passa a ser responsabilidade do adaptador da API. Isso permite compartilhar dados entre módulos independentes de forma transparente.

Isso permite uma reduzção das chamadas de API drasticamente. Esse cache fica em memória e é perdido ao recarregar a página. Isso pode ser evitado se as informações forem persistidas no local storage.

## Cache de API em módulo ou componente

Um outra alternativa, que apesar de mais simples, é mais fácil de se implementar. Nesse caso, existem basicamente duas estratégias:

1. Criar um serviço que gerencia um lazy cache e injetá-lo no componente raiz. Essa informação é mantida somente durante permanência na rota raiz.
Útil em componentes complexos com uma rota base em comum (agenda por exemplo).

1. Uma outra forma envolve criar esse mesmo serviço e provisioná-lo em um módulo que contenha os componentes onde se deseja aplicar o comportamento. Útil para compartilhar requests entre funcionalidade relacionadas que não necessariamente possum um rota base.

## Recomendação
Não fazer o uso de cache a não ser que seja necessário (impacto na API, ou melhorar a usabilidade em algum caso específico). Entretanto, quando necessário em algum lugar específico, fazer uso aplicando em módulo/componente. Preferência para uso em componente para não reter o estado por muito tempo. Quando for usado a estratégia do módulo (2) é necessário estratégia para resetar/invalidar essa informação.


# Vazamento de memória em controladores

Os elementos do Angular (Componente, Diretiva, etc…) possuem vários hooks de lifecycle: ngOnChanges(), ngOnInit(), ngAfterContentInit(), ngAfterViewInit(), ngOnDestroy(), entre outros sendo que Os mais essenciais e frequentemente usados são ngOnInit e ngOnDestroy.

O primeiro Roda quando componente é inserido na tela (troca de rota, *ngIf, …) e, geralmente, onde se obtém dados da API para renderização. Já o ngOnDestroy é executado instantes antes de ser removido do DOM. Lugar apropriado fazer o clean up do estado do componente. Quando isso não é feito pode causar (i) Erro ao tentar alterar componente que não existe mais no DOM e (ii) vazamento de memória.

No geral, a principal causa desses erros é não fazer o clean up de Observables. Para fazer esse processo basta implementar o método ngOnDestroy e usar o método unsubscribe() na Subscription de cada observable. A Subscription é retorando ao se fazer o subscribe nesses elementos. Um boa regra a se seguir para cada subscribe feito em um componente deve haver um unsubscribe.

## Fazendo o uso so Async pipe

Para facilitar o gerenciamento e clean-ups de Observables existe um pipe padrão do Angular chamado Async, mais detalhes sobre ele podem ser vistos [aqui](https://angular.io/api/common/AsyncPipe). Esse pipe tem os seguintes comportamentos:
1. O pipe faz o subscribe em Promise e Observables.
2. Quando novas informações são emitidas marca o componente como “check for changes”. Ou seja, o template atualiza de forma automática.
3. Faz o unsubscribe de forma automática no ngOnDestroy event.
4. Caso a referência ao Observable/Promise mude o pipe automaticamente (1) faz unsubscribe do antigo e (2) faz subscribe do novo.

### Exemplo
Controlador

```
...
constructor(private pessoaService: PessoaService) {}

ngOnInit() {
    const filtro = { currentPage: 1, pageSize: 10 };

    /*
        * Exemplo de como operadores podem ser utilizados.
        * O retorno do método pipe também é um observable.
        */
    this.pessoas$ = this.pessoaService.getPeople(filtro).pipe(
        map(result => result.data)
    );
}
...
```

Template

```
<div class="pessoa" *ngFor="let pessoa of pessoas$ | async">
    {{ pessoa.nome }}
</div>
```


