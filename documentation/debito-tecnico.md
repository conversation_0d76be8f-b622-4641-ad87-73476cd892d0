É importante que os times invistam um parcela de tempo de forma recorrente em melhorias com essas para garantir que a aplicação esteja sempre atualizada e em bom estado.

# Adaptadores de API

Melhorias referentes aos adapters de API. Usar como referência os padrões definidos [aqui.](/documentation/adaptadores-api.md)


## 1. Tipagem do adapters
Vários dos métodos dos serviços definidos nos adapters estão com tipo *any*. Isso dificulta a utilização desses adapters pois não fornece uma proteção contra tipagens incorretas durante o build bem como não disponibilizam autocomplete durante o desenvolvimento. Criar tipos para esses métodos tem um ótimo custo beneficio por ser bem fácil de fazer. Lembrar de usar nomes para as interfaces que comuniquem exatamente o que aquela interface representa, por exemplo, TreinoFichaAtividade é um nome muito mais útil doque Atividade. Alguns dos serviços que estão sem tipos são:

1. ProdutoApiBalancoService
2. RelatorioApiColaboradorService
3. RelatorioApiRelatorioPersonalService
4. RelatorioApiBaseService

## 2. Compartilhar modelos entre adapters

Atuamente existem vários adapters na plataforma e mais estão sendo criados. Cada um deles define um config provider que permite que a aplicação que esteja fazendo o uso do mesmo faça sua configuração, por exemplo, PlanoApiConfigProviderBase. Entretanto, várias das apis precisam de configurações bem parecidas com a do PlanoApi

```
export interface PlanoApiConfig {
    baseUrl: string;
    authToken: string;
}
```

Portanto poderiamos criar um ProviderGenerico que seja utilizado por várias apis similares e que fosse movido para um biblioteca de utilitário (SDK poderia conter essas informações). Esse provider seria algo do tipo:

```
export interface BaseApiConfig {
    baseUrl: string;
    authToken: string;
    empresaId: string;
}

@Injectable()
export abstract class BaseApiConfigProviderBase {

    abstract getApiConfig(): Observable<BaseApiConfig>;
}

```
Dessa forma uma aplicação que precisar fazer o uso de vários dessas API's poderia implementar apenas um único serviço Provider e usá-lo para configurar várias adapters de uma só vez, simplificando os projetos.


## 3. Migrar adapters para bibliotecas
Grande parte dos serviços de comunicação com API's que existian na 'plataforma-pacto' e 'adm' foram migrados para seus respectivos adapters. Porém, ainda existem alguns que não foram extraídos. Alguns deles são:

1. Adapter do graduação (src\app\microservices\graduacao)
2. Personagem (src\app\microservices\personagem)
3. EmpresaService (projects\adm\src\app\relatorios\services\empresa.service.ts)
4. EmpresaService (projects\adm\src\app\produtos\services\empresa\empresa.service.ts)
5. EmpresaService (projects\adm\src\app\cadastro-auxliar\services\empresa\empresa.service.ts)
6. ProdutoService
7. BalancoItemService
8. CategoriaProdutoService
9. ComissaoProdutoConfiguracaoService
10. ProdutoEstoqueService

# Refatorar elementos de construção de tabelas dinâmicas
Usar os padrões discutidos nesse [documento](/documentation/data-tables.md) para implementar diretivas e compontes que permitam a construção fácil e sustentável de tabelas dinâmicas. Ao longo desse processo, deve-se depreciar o uso de todos os componentes atualmente existentes para construção de relatórios. 

# Regatorar elementos de construção de formulários
Usar os padrões discutidos nesse [documento](/documentation/formularios.md) para implementar os componentes base que serão usados para construção de formulários.

# Boas práticas na gestão do catálogo de componentes
Aplicar conceitos e boas práticas definidos [aqui](/documentation/boas-praticas-ui-kit.md) durante construção de componentes. Além disso, incluir padrões propostos no processo de code review.

# Padrões de qualidade na organização de controladores e rotas
Aplicar conceitos definidos [aqui](/documentation/controladores-e-rotas.md) durante criação rotas, especialmente em features complexas. Além disso, incluir padrões propostos no processo de code review.

# Padrões e boas práticas ao fragmentar plataforma em aplicações
Aplicar conceitos e padrões definidos [aqui](/documentation/fragmentando-plataforma.md) ao dividir plataforma em aplicações e compartilhar código em bibiotecas. Fragmentar a aplicação "plataforma-pacto" em pedaços menores (treino, pactoPay, agenda, pessoas). Além disso, incluir padrões propostos no processo de code review.