
A plataforma é um monorepo com todas as aplicações front da plataforma na linguagem Angular (atualmente utilizando a versão 8). Esse monorepo constitui de algumas aplicações e também algumas bibliotecas. Essas bibliotecas contem código em comum entre as aplicações que podem ser utilizados.

## NX Cli
Para gerenciar essas elementes e as dependências entre elas faremos o uso da ferrametna [NX](https://nx.dev/). Essa ferramenta tem suporte para projetos em várias linguagens, em especial Angular e React.

Para lidar com projetos em Angular o NX, internamente, usa o [ng](https://angular.io/cli). Dessa forma os comandos que são normalmente são usados no desenvolvimento de aplicações Angular como:

```
    ng generate component
    ng serve app
    ng build app
    ...
```
Funcionam da mesma forma, com a única diferença que usamos o CLI do NX:
```
    nx generate component
    nx serve app
    nx build app
```

## Integração com Angular

O nx possui plugins feitos pelo próprio time do NX que permite um alto nível de integração com várias tecnologias diferentes. No nosso caso fazemos o uso do plugin para Angular [@nrwl/angular](https://nx.dev/packages/angular). Para mais informações sobre quais comandos estão disponíveis lembre-se de consultar essa documentação.

## Vantagens do NX
* NX faz tudo que o CLI faz e muito mas
* Builds mais rápidas (faz uso de cache)
* Suporte para micro-front (isso permitiria migração gradual para outras tecnologias)
* Gráfico de dependência
* Build automatizadas de bibliotecas
* Atualizar versão do angular (nx upgrade)

## Organização básica do repositório

Cada produto da plataforma (treino, adm, pactopay) deve uma aplicação independente, buildade e deployada de forma independente. O código em comum entre essas aplicações deve ser organizado em bibliotecas da seguinte forma:

* Componentes de interface que sejam relevamente e necessários em várias aplicações diferentes deve ficar na biblioteca UI-kit.
* Código referente a comunicação com API's bem como os modelos associados a eles deve ficar em bibliotecas, um adapter para cada API. Detalhes de como isso deve ser feito com vários exemplos podem ser encontrados [aqui](/documentation/adaptadores-api.md).
* Utilitários diversos, serviços de gestão de sessão e configurações genéricas devem ser colocadas no SDK.

Obs: o conceito de [aplicação](https://angular.io/cli/generate#application-command) e [biblioteca](https://angular.io/guide/creating-libraries) que estamos nos referindo aqui faz referência aos mesmos conceitos definidos e utilizados pelo Angular cli.

## Compartilhando estado entre as aplicações

Para compartilhar informações entre as diferentes aplicações (treino, adm e outros) deve se usar o localStorage. Para que isso seja possível as aplicações devem ser deployadas na mesma *origin*, ou seja, mesmo domínio e porta. Essa solução traz algumas limitações porem é fácil de se implementar.
Posteriormente, um ponto de melhoria seria fazer o uso de micro-frontend para "colar" as aplicações e compartilhar essas informações.

## Gerenciando Aplicações

### Criando 
Para se criar um aplicação basta rodar o comando:

```
> nx generate @nrwl/angular:application
```

Entretanto, como no nosso repositório fazemos o uso apenas do plugin Angular podemos omitir o trecho ***@nrwl/angular*** e escrever:
```
> nx generate application
```
Isso vale também para os próximos comandos.

### Rodar localmente & buildar
```
> nx serve app-name

> nx build app-name
```
Para passar atributo adicionais para o ng usado internamente pasta colocar no fim do comando:

```
> nx build app-name --aot
```

### Lint & Test
Buildar:
```
> nx build app-name
```
Lintar:
```
> nx build app-name
```


## Gerenciando Bibliotecas
Generate:
```
> nx generate lib lib-name
```
Lint:
```
> nx lint lib-name
```
Test
```
> nx test lib-name
```
Obs: não é necessário buildar as bibliotecas para usar, isso é gerenciado automaticmente pelo NX.

### Usando Bibliotecas

Não é necessário fazer nenhum tipo de build das bibliotecas para fazer o uso, basta usar os componentes necessários nas aplicações. Entretanto, antes de usar uma lib é ncessário importar o módulo principal na aplicação que fará o uso:

```
import { UiModule } from '@pacto/ui';

@NgModule({
    ...
    imports: [
        ...
        UiModule
        ...
    ]
    ...
})
```
No exemplo acima **'@pacto'** represente o workpace do repositório e deve ser usado para importar qualquer biblioteca.

Para usar basta chamar diretamente no template:
```
<pacto-button/>
```
Ou em um controller:
```
import { UtilService } from "@pacto/sdk";

@Componente({...})
export ListaAlunoComponent {

    constructor(private utils: UtilsService) {
        ...
    }

}

```

Pronto! Agora basta rodar a aplicação com **nx serve** ou buildar com nx **build** e o NX vai gerenciar a dependencia de forma automática.


## Visualizango as dependencias

Com o tempo, o monorepo vai ser tornar complexo e cheio de dependencias internas. Para facilitar o entendimento de como a aplicação está organizada o NX forneçe um utilitário que cria um diagrama visual que demonstra como os módulos dependem uns dos outros. Basta rodar o comando:
```
> nx dep-graph
```
e acessar no browser:
```
http://localhost:4211
```

## Módulos afetados por uma mudança

Nx possui uma série de utilitários que nos permite visualizar quais partes da aplicação foram afetadas por uma mudança. Isso nos da a opção de buildar, lintar e testar apenas as partes do sistema que possivelmente foram impactadas.

Para visualizar quais módulos (app e libs) foram afetas pelas mudanças feitas (considerado um diff entre o branch atual e a master) basta rodar:
```
> nx affected:dep-graph
```

Para buildar, lintar ou testar apenas as applicações afetadas por tais mudanças:
```
> nx affected:build

> nx affected:lint

> nx affected:test
```

## Divisão de módulos

Aplicações monorepo, caso não sejam monitoradas, se tornam complexas e cheias de dependências internas. O NX possui algumas ferramentas para ajudar a controlar esse problema.

Para isso categorizamos cadas módulo (**aplications** & **libraries**) com uma das seguintes **tags**:

* Aplicações: **type:app**
* Adapter de api: **type:api**
* Catálogo de componentes: **type:ui**
* Utilitários: **type:utils**

Para fazer isso configuramos o arquivo nx.json, configurando cada item do objeto "projects" seguindo o seguinte exemplo:
```
    "treino": {
        "tags": ["type:app"]
    },
    ...
```

Em seguinda, definos uma série de regras sobre de quais módulos um tipo de módulo pode depender:

* Aplicações
  * Podem usar (adapter, catálogo ui e utilitários)
* Adapter de Api
  * Podem usar apenas utilitários
* Catálogo de componentes
  * Podem usar apenas utilitários
* Utilitário
  * Podem usar apenas utilitários

Para fazer com essas regras sejam respeitadas podem configurar uma regra de lint que nos informa quando essas regras estão sendo quebrardas. Isso pode ser utilizado no pipeline para impedir esse tipo de erro.

A ferramenta NX possui um regra de lint customizada chama "nx-enforce-module-boundaries". Para configurá-la basta seguir o modelo abaixo:

```
{
  ...
  "rules": [
    ...
    "nx-enforce-module-boundaries": [
      true,
      {
        "enforceBuildableLibDependency": true,
        "allow": [],
        "depConstraints": [
          {
            "sourceTag": "type:app",
            "onlyDependOnLibsWithTags": [
              "type:ui",
              "type:utils",
              "type:api"
            ]
          },
          ...
        ]
      }
    ],
    ...
  ]
  ...
}

```

Adicionando um objeto como esse:
```
{
  "sourceTag": "type:ui",
  "onlyDependOnLibsWithTags": ["type:utils", ...]
}
```
Para cada um dos tipos.

Com essas configurações feitas, caso uma dessas regras seja quebrada o lint emitirá um erro quando for executado.